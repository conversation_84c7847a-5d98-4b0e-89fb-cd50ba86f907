{"version": 3, "file": "forum.js", "sources": ["../src/forum/model/transfer-money.ts", "../src/forum/components/sources/user-search-source.tsx", "../src/forum/constants/index.ts", "../src/forum/components/transfer-money-search-modal.tsx", "../src/forum/components/transfer-money-success-modal.tsx", "../src/forum/components/transfer-money-modal.tsx", "../src/forum/components/transfer-money-notification.ts", "../src/forum/components/transfer-history-list-item.tsx", "../src/forum/components/transfer-history-list.tsx", "../src/forum/components/transfer-history-page.tsx", "../src/forum/add-transfer-money-page.ts", "../src/forum/add-client1-customization-features.ts", "../src/forum/index.ts"], "sourcesContent": ["import Model from 'flarum/common/Model';\nimport type { TransferMoneyModel, UserWithMoney } from '../types';\n\n/**\n * Model for transfer money records with strict typing\n */\nexport default class TransferMoney extends Model implements TransferMoneyModel {\n  /**\n   * Get the unique identifier for this transfer\n   */\n  id(): string {\n    return Model.attribute<string>('id').call(this) || '';\n  }\n\n  /**\n   * Get the transfer amount\n   */\n  transferMoney(): number {\n    const DEFAULT_AMOUNT = 0;\n    const amount = Model.attribute<number>('transfer_money_value').call(this);\n    if (amount === null || typeof amount === 'undefined') {\n      return DEFAULT_AMOUNT;\n    }\n    return amount;\n  }\n\n  /**\n   * Get the transfer notes\n   */\n  notes(): string | null {\n    return Model.attribute<string | null>('notes').call(this);\n  }\n\n  /**\n   * Get the assignment date\n   */\n  assignedAt(): Date {\n    const dateStr = Model.attribute<string>('assigned_at').call(this);\n    if (dateStr) {\n      return new Date(dateStr);\n    }\n    return new Date();\n  }\n\n  /**\n   * Get the user who initiated the transfer\n   */\n  fromUser(): UserWithMoney {\n    return Model.hasOne<UserWithMoney>('fromUser').call(this);\n  }\n\n  /**\n   * Get the user who received the transfer\n   */\n  targetUser(): UserWithMoney {\n    return Model.hasOne<UserWithMoney>('targetUser').call(this);\n  }\n\n  /**\n   * Get the creation date\n   */\n  createdAt(): Date {\n    const dateStr = Model.attribute<string>('created_at').call(this);\n    if (dateStr) {\n      return new Date(dateStr);\n    }\n    return new Date();\n  }\n\n  /**\n   * Get the last update date\n   */\n  updatedAt(): Date {\n    const dateStr = Model.attribute<string>('updated_at').call(this);\n    if (dateStr) {\n      return new Date(dateStr);\n    }\n    return new Date();\n  }\n}\n\n// Map API attributes/relationships to model accessors for backward compatibility\nObject.assign(TransferMoney.prototype, {\n  // id is provided by the API\n  id: Model.attribute<string>('id'),\n  transferMoney: Model.attribute<number>('transfer_money_value'),\n  notes: Model.attribute<string | null>('notes'),\n  assignedAt: Model.attribute<string>('assigned_at'),\n  fromUser: Model.hasOne('fromUser'),\n  targetUser: Model.hasOne('targetUser'),\n});\n", "import type Mithril from 'mithril';\nimport username from 'flarum/common/helpers/username';\nimport avatar from 'flarum/common/helpers/avatar';\nimport highlight from 'flarum/common/helpers/highlight';\n\nconst MIN_QUERY_LENGTH = 3;\nconst SEARCH_LIMIT = 5;\n\nexport default class UserSearchSource {\n  private results: unknown[] = [];\n\n  search(query: string): Promise<void> {\n    if (!query || query.length < MIN_QUERY_LENGTH) {\n      this.results = [];\n      return Promise.resolve();\n    }\n\n    return app.store.find('users', { filter: { query }, page: { limit: SEARCH_LIMIT } })\n      .then((results) => {\n        this.results = results;\n      });\n  }\n\n  view(query: string): Mithril.Vnode[] {\n    if (!this.results || !this.results.length) {\n      return [];\n    }\n\n    return this.results.map((user: unknown) => (\n      <li className=\"SearchResult\" data-index={`users:${(user as { id(): string }).id()}`}>\n        <a className=\"SearchResult\" tabindex=\"-1\">\n          {avatar(user)} {username(user)}\n          <span className=\"SearchResult-excerpt\">{highlight((user as { username(): string }).username(), query)}</span>\n        </a>\n      </li>\n    ));\n  }\n}\n", "/**\n * Constants for the money transfer extension\n */\n\n// Default values\nexport const DEFAULT_MONEY_AMOUNT = 0;\nexport const DEFAULT_USER_COUNT = 0;\nexport const MINIMUM_TRANSFER_AMOUNT = 0;\n\n// UI Constants\nexport const MODAL_PADDING_BOTTOM = '20px';\n\n// Permission constants\nexport const PERMISSION_TRANSFER_MONEY = 'transferMoney.allowUseTransferMoney';\n\n// Default money name placeholder\nexport const DEFAULT_MONEY_NAME = '[money]';\n\n// Money attribute key\nexport const MONEY_ATTRIBUTE_KEY = 'antoinefr-money.moneyname';\n\n// User selection prefix\nexport const USER_SELECTION_PREFIX = 'users:';\n\n// Default user selection count\nexport const DEFAULT_USER_SELECTION_COUNT = 1;\n\n// Search modal constants\nexport const RANDOM_STRING_BASE = 36;\nexport const RANDOM_STRING_START_INDEX = 2;\nexport const SEARCH_TYPING_DELAY = 900;\nexport const MIN_SEARCH_LENGTH = 3;\nexport const EMPTY_ARRAY_LENGTH = 0;\nexport const FIRST_ARRAY_INDEX = 0;\nexport const SECOND_ARRAY_INDEX = 1;\n\n// Pagination constants\nexport const STANDARD_ITEMS_PER_PAGE = 20;\n\n// UI Constants\nexport const TEXTAREA_MAX_LENGTH = 255;\nexport const BUTTON_WIDTH = '66px';\nexport const CONTAINER_HEIGHT = '34px';\n\n// Route constants\nexport const ROUTE_TAGS = 'tags';\n\n// CSS Display values\nexport const CSS_DISPLAY_NONE = 'none';\nexport const CSS_VISIBILITY_HIDDEN = 'hidden';\n\n// Extension constants\nexport const EXTENSION_FLARUM_SUSPEND = 'flarum-suspend';\nexport const CUSTOMIZATION_ENABLED = '1';\n", "import app from 'flarum/forum/app';\nimport Search from 'flarum/forum/components/Search';\nimport UserSearchSource from './sources/user-search-source';\nimport ItemList from 'flarum/common/utils/ItemList';\nimport classList from 'flarum/common/utils/classList';\nimport extractText from 'flarum/common/utils/extractText';\nimport LoadingIndicator from 'flarum/common/components/LoadingIndicator';\nimport username from 'flarum/common/helpers/username';\nimport avatar from 'flarum/common/helpers/avatar';\nimport type Mithril from 'mithril';\nimport type User from 'flarum/common/models/User';\nimport {\n  RANDOM_STRING_BASE,\n  RANDOM_STRING_START_INDEX,\n  SEARCH_TYPING_DELAY,\n  MIN_SEARCH_LENGTH,\n  EMPTY_ARRAY_LENGTH\n} from '../constants';\n\ninterface TransferMoneySearchModalAttrs {\n  selected: unknown;\n  selectedUsers: Record<string, number>;\n  needMoney: unknown;\n  callback: () => void;\n}\n\nexport default class TransferMoneySearchModal extends Search<TransferMoneySearchModalAttrs> {\n  private inputUuid!: string;\n  private typingTimer?: number;\n  private doSearch = false;\n\n  oninit(vnode: Mithril.Vnode<TransferMoneySearchModalAttrs, this>): void {\n    super.oninit(vnode);\n    this.inputUuid = Math.random().toString(RANDOM_STRING_BASE).substring(RANDOM_STRING_START_INDEX);\n  }\n\n  oncreate(vnode: Mithril.VnodeDOM<TransferMoneySearchModalAttrs, this>): void {\n    super.oncreate(vnode);\n\n    this.$('.Search-results').on('click', () => {\n      const target = this.$('.SearchResult.active');\n      this.addRecipient(target.data('index') as string);\n      this.$('.RecipientsInput').focus();\n    });\n\n    this.$('.Search-results').on('touchstart', (event: TouchEvent) => {\n      const target = this.$(event.target as Element).parent();\n      this.addRecipient(target.data('index') as string);\n      this.$('.RecipientsInput').focus();\n    });\n\n    (globalThis as { jquery: (selector: string) => JQuery }).jquery('.RecipientsInput')\n      .on('input', () => {\n        clearTimeout(this.typingTimer);\n        this.doSearch = false;\n        this.typingTimer = globalThis.setTimeout(() => {\n          this.doSearch = true;\n          m.redraw();\n        }, SEARCH_TYPING_DELAY);\n      })\n      .on('keydown', () => {\n        clearTimeout(this.typingTimer);\n      });\n\n    super.oncreate(vnode);\n  }\n\n  view(): Mithril.Children {\n    if (typeof this.searchState.getValue() === 'undefined') {\n      this.searchState.setValue('');\n    }\n\n    const searchValue = this.searchState.getValue();\n    const loading = searchValue && searchValue.length >= MIN_SEARCH_LENGTH;\n\n    if (!this.sources) {\n      this.sources = this.sourceItems().toArray();\n    }\n\n    const selectedUserArray = (this.attrs.selected as () => ItemList<User>)().toArray();\n\n    return (\n      <div className=\"transferSearchContainer\">\n        <div className=\"transferSearchUserListContainer\">\n          {selectedUserArray.length === EMPTY_ARRAY_LENGTH && (\n            <div style=\"height:34px;cursor: default !important;\" class=\"transferSearchUserContainer\">\n              <span class=\"transferSearchUser\">{app.translator.trans('wusong8899-transfer-money.forum.transfer-search-no-user-selected')}</span>\n            </div>\n          )}\n\n          {selectedUserArray\n            .map((recipient: User) => {\n              const userName = username(recipient);\n              const userAvatar = avatar(recipient);\n\n              return (\n                <div class=\"transferSearchUserContainer\" onclick={(event: MouseEvent) => this.removeRecipient(recipient, event)}>\n                  <span class=\"transferSearchUser\">{userAvatar}</span> {userName}\n                </div>\n              );\n            })}\n        </div>\n\n        <div className=\"Search\">\n          <div className=\"Search-input\">\n            <input\n              className=\"RecipientsInput FormControl\"\n              type=\"search\"\n              placeholder={extractText(app.translator.trans('wusong8899-transfer-money.forum.transfer-search-placeholder'))}\n              value={this.searchState.getValue()}\n              oninput={(event: InputEvent) => this.searchState.setValue((event.target as HTMLInputElement).value)}\n              onfocus={() => (this.hasFocus = true)}\n              onblur={() => (this.hasFocus = false)}\n            />\n            <ul className={classList('Dropdown-menu', 'Search-results', 'fade', { in: !!loading })}>\n              {this.renderSearchResults()}\n            </ul>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  private renderSearchResults(): Mithril.Children {\n    if (!this.doSearch) {\n      return LoadingIndicator.component({ size: 'tiny', className: 'Button Button--icon Button--link' });\n    }\n\n    return this.sources.map((source: UserSearchSource) => source.view(this.searchState.getValue()));\n  }\n\n  sourceItems(): ItemList<UserSearchSource> {\n    const items = new ItemList<UserSearchSource>();\n    items.add('users', new UserSearchSource());\n    return items;\n  }\n\n  addRecipient(value: string): void {\n    const [type, id] = value.split(':');\n    const recipient = this.findRecipient(type, id);\n\n    if (recipient) {\n      (this.attrs.selected as () => ItemList<User>)().add(value, recipient);\n      this.attrs.selectedUsers[recipient.data.id] = 1;\n      (this.attrs.needMoney as (value: number) => void)(this.getNeedMoney());\n      this.searchState.setValue('');\n    }\n  }\n\n  removeRecipient(recipient: User, event: Event): void {\n    event.preventDefault();\n\n    const userID = recipient.data.id;\n    Reflect.deleteProperty(this.attrs.selectedUsers, userID);\n\n    (this.attrs.selected as () => ItemList<User>)().remove('users:' + userID);\n    (this.attrs.needMoney as (value: number) => void)(this.getNeedMoney());\n  }\n\n  getNeedMoney(): number {\n    const inputElement = document.getElementById('moneyTransferInput') as HTMLInputElement;\n    if (!inputElement) {\n      return EMPTY_ARRAY_LENGTH;\n    }\n    const moneyTransferValue = Number.parseFloat(inputElement.value);\n    return moneyTransferValue * Object.keys(this.attrs.selectedUsers).length;\n  }\n\n  findRecipient(store: string, id: string): User | null {\n    return app.store.getById(store, id) as User | null;\n  }\n}\n", "import app from 'flarum/forum/app';\nimport Modal from 'flarum/common/components/Modal';\nimport Button from 'flarum/common/components/Button';\nimport type Mithril from 'mithril';\nimport { BUTTON_WIDTH } from '../constants';\n\nexport default class TransferMoneySuccessModal extends Modal {\n  static isDismissible = false;\n\n  oninit(vnode: Mithril.Vnode<unknown, this>): void {\n    super.oninit(vnode);\n  }\n\n  className(): string {\n    return 'Modal--small';\n  }\n\n  title(): string {\n    return app.translator.trans('wusong8899-transfer-money.forum.transfer-money-success');\n  }\n\n  content(): Mithril.Children {\n    return [\n      <div className=\"Modal-body\">\n        <div style=\"text-align:center\">\n          {Button.component(\n            {\n              style: `width:${BUTTON_WIDTH}`,\n              className: 'Button Button--primary',\n              onclick: () => {\n                location.reload();\n              },\n            },\n            app.translator.trans('wusong8899-transfer-money.forum.ok')\n          )}\n        </div>\n      </div>,\n    ];\n  }\n}\n", "import app from 'flarum/forum/app';\nimport Modal from 'flarum/common/components/Modal';\nimport Button from 'flarum/common/components/Button';\nimport SearchState from 'flarum/forum/states/SearchState';\nimport ItemList from 'flarum/common/utils/ItemList';\nimport Stream, { type Stream as StreamType } from 'flarum/common/utils/Stream';\nimport Alert from 'flarum/common/components/Alert';\nimport type Mithril from 'mithril';\n\nimport TransferMoneySearchModal from './transfer-money-search-modal';\nimport TransferMoneySuccessModal from './transfer-money-success-modal';\nimport type {\n  TransferMoneyModalAttrs,\n  TransferMoneyResponsePayload\n} from '../types';\nimport {\n  DEFAULT_MONEY_AMOUNT,\n  DEFAULT_USER_COUNT,\n  MINIMUM_TRANSFER_AMOUNT,\n  DEFAULT_MONEY_NAME,\n  MONEY_ATTRIBUTE_KEY,\n  USER_SELECTION_PREFIX,\n  DEFAULT_USER_SELECTION_COUNT,\n  TEXTAREA_MAX_LENGTH\n} from '../constants';\n\nexport default class TransferMoneyModal extends Modal<TransferMoneyModalAttrs> {\n  static isDismissible = false;\n\n  private selected!: StreamType<ItemList<unknown>>;\n  private selectedUsers!: Record<string, number>;\n  private moneyName!: string;\n  private recipientSearch!: SearchState;\n  private needMoney!: StreamType<number>;\n\n  oninit(vnode: Mithril.Vnode<TransferMoneyModalAttrs, this>): void {\n    super.oninit(vnode);\n    // eslint-disable-next-line new-cap\n    this.selected = Stream(new ItemList());\n    this.selectedUsers = {};\n    this.moneyName = app.forum.attribute(MONEY_ATTRIBUTE_KEY) as string || DEFAULT_MONEY_NAME;\n\n    const targetUser = this.attrs.user;\n    if (targetUser) {\n      this.selected().add(USER_SELECTION_PREFIX + targetUser.id(), targetUser);\n      this.selectedUsers[targetUser.id()] = DEFAULT_USER_SELECTION_COUNT;\n    }\n\n    this.recipientSearch = new SearchState();\n    // eslint-disable-next-line new-cap\n    this.needMoney = Stream(DEFAULT_MONEY_AMOUNT);\n  }\n\n  className(): string {\n    return 'Modal--small';\n  }\n\n  title(): string {\n    return app.translator.trans('wusong8899-transfer-money.forum.transfer-money');\n  }\n\n  private getCurrentUserMoney(): number {\n    if (!app.session.user) {\n      return DEFAULT_MONEY_AMOUNT;\n    }\n    const money = app.session.user.attribute('money');\n    if (typeof money === 'number') {\n      return money;\n    }\n    return DEFAULT_MONEY_AMOUNT;\n  }\n\n  private getMoneyTransferInputValue(): number {\n    const inputElement = document.getElementById('moneyTransferInput') as HTMLInputElement;\n    if (!inputElement) {\n      return DEFAULT_MONEY_AMOUNT;\n    }\n    const value = Number.parseFloat(inputElement.value);\n    if (Number.isNaN(value)) {\n      return DEFAULT_MONEY_AMOUNT;\n    }\n    return value;\n  }\n\n  private getMoneyTransferNotesValue(): string {\n    const inputElement = document.getElementById('moneyTransferNotesInput') as HTMLInputElement;\n    if (!inputElement) {\n      return '';\n    }\n    return inputElement.value;\n  }\n\n  private handleRedraw = (): void => {\n    m.redraw();\n  };\n\n  content(): Mithril.Children {\n    return (\n      <div className=\"Modal-body\">\n        <div className=\"Form\">\n          <div style=\"padding-bottom:20px;\" className=\"TransferMoneyModal-form\">\n            {TransferMoneySearchModal.component({\n              state: this.recipientSearch,\n              selected: this.selected,\n              selectedUsers: this.selectedUsers,\n              needMoney: this.needMoney,\n              callback: this.handleRedraw,\n            })}\n          </div>\n\n          <div className=\"Form-group\">\n            <label>\n              {app.translator.trans('wusong8899-transfer-money.forum.current-money-amount')}\n              {this.moneyName.replace('[money]', String(this.getCurrentUserMoney()))}\n            </label>\n            <input\n              id=\"moneyTransferInput\"\n              placeholder={app.translator.trans('wusong8899-transfer-money.forum.transfer-money-input-placeholder')}\n              required\n              className=\"FormControl\"\n              type=\"number\"\n              step=\"any\"\n              min=\"0\"\n              oninput={() => this.moneyTransferChanged()}\n            />\n            <div style=\"padding-top:10px\">\n              {app.translator.trans('wusong8899-transfer-money.forum.need-money-amount')}\n              <span id=\"needMoneyContainer\">{this.moneyName.replace('[money]', String(this.needMoney()))}</span>\n            </div>\n          </div>\n\n          <div className=\"Form-group\">\n            <label>{app.translator.trans('wusong8899-transfer-money.forum.transfer-money-notes')}</label>\n            <textarea id=\"moneyTransferNotesInput\" maxlength={TEXTAREA_MAX_LENGTH} className=\"FormControl\" />\n          </div>\n\n          <div className=\"Form-group\" style=\"text-align: center;\">\n            {Button.component(\n              {\n                className: 'Button Button--primary',\n                type: 'submit',\n                loading: this.loading,\n              },\n              app.translator.trans('wusong8899-transfer-money.forum.ok')\n            )}\n            &nbsp;\n            {Button.component(\n              {\n                className: 'Button transferMoneyButton--gray',\n                loading: this.loading,\n                onclick: () => {\n                  this.hide();\n\n                  if (this.attrs.callback && typeof this.attrs.callback === 'function') {\n                    this.attrs.callback();\n                  }\n                },\n              },\n              app.translator.trans('wusong8899-transfer-money.forum.cancel')\n            )}\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  private getTotalNeedMoney(): number {\n    const inputElement = document.getElementById('moneyTransferInput') as HTMLInputElement;\n    if (!inputElement) {\n      return DEFAULT_MONEY_AMOUNT;\n    }\n\n    let moneyTransferValue = Number.parseFloat(inputElement.value);\n\n    if (Number.isNaN(moneyTransferValue)) {\n      moneyTransferValue = DEFAULT_MONEY_AMOUNT;\n    }\n\n    return Object.keys(this.selectedUsers).length * moneyTransferValue;\n  }\n\n  private moneyTransferChanged(): void {\n    const totalNeedMoney = this.getTotalNeedMoney();\n    const totalNeedMoneyText = this.moneyName.replace('[money]', String(totalNeedMoney));\n    const needMoneyContainer = document.getElementById('needMoneyContainer');\n    if (needMoneyContainer) {\n      needMoneyContainer.textContent = totalNeedMoneyText;\n    }\n  }\n\n  onsubmit(event: Event): void {\n    event.preventDefault();\n    const userMoney = this.getCurrentUserMoney();\n    const moneyTransferValue = this.getMoneyTransferInputValue();\n    const moneyTransferValueTotal = this.getTotalNeedMoney();\n    const moneyTransferNotesValue = this.getMoneyTransferNotesValue();\n\n    if (moneyTransferValueTotal > userMoney) {\n      app.alerts.show(Alert, { type: 'error' }, app.translator.trans('wusong8899-transfer-money.forum.transfer-error-insufficient-fund'));\n      return;\n    }\n\n    if (Object.keys(this.selectedUsers).length === DEFAULT_USER_COUNT) {\n      app.alerts.show(\n        Alert,\n        { type: 'error' },\n        app.translator.trans('wusong8899-transfer-money.forum.transfer-error-no-target-user-selected')\n      );\n      return;\n    }\n\n    if (moneyTransferValue > MINIMUM_TRANSFER_AMOUNT) {\n      const moneyTransferData = {\n        moneyTransfer: moneyTransferValue,\n        moneyTransferNotes: moneyTransferNotesValue,\n        selectedUsers: JSON.stringify(Object.keys(this.selectedUsers)),\n      };\n\n      this.loading = true;\n\n      app.store\n        .createRecord('transferMoney')\n        .save(moneyTransferData)\n        .then((payload: TransferMoneyResponsePayload) => {\n          app.store.pushPayload(payload);\n          app.modal.show(TransferMoneySuccessModal);\n          this.loading = false;\n\n          if (this.attrs.callback && typeof this.attrs.callback === 'function') {\n            this.attrs.callback();\n          }\n        })\n        .catch(() => {\n          this.loading = false;\n        });\n    }\n  }\n}\n\n", "import app from 'flarum/forum/app';\nimport Notification from 'flarum/forum/components/Notification';\nimport type NotificationModel from 'flarum/common/models/Notification';\n\nexport default class TransferMoneyNotification extends Notification<{ notification: NotificationModel }> {\n  icon(): string {\n    return 'fas fa-money-bill';\n  }\n\n  href(): string {\n    const { user } = app.session;\n    let username = '';\n    if (user) {\n      username = user.username();\n    }\n    return app.route('user.transferHistory', { username });\n  }\n\n  content(): Mithril.Children {\n    const user = this.attrs.notification.fromUser();\n    return app.translator.trans(\n      'wusong8899-transfer-money.forum.notifications.user-transfer-money-to-you',\n      {\n        user,\n      }\n    );\n  }\n\n  excerpt(): Mithril.Children {\n    const notification = this.attrs.notification.subject() as unknown;\n    let transferMoney: unknown = '';\n    let transferID: unknown = '';\n\n    if (notification && (notification as { attribute?: Function }).attribute) {\n      transferMoney = (notification as { attribute: Function }).attribute('transfer_money_value');\n      transferID = (notification as { attribute: Function }).attribute('id');\n    }\n    const moneyName = app.forum.attribute('antoinefr-money.moneyname') || '[money]';\n    const costText = moneyName.replace('[money]', transferMoney);\n\n    return app.translator.trans(\n      'wusong8899-transfer-money.forum.notifications.user-transfer-money-details',\n      {\n        cost: costText,\n        id: transferID,\n      }\n    );\n  }\n}\n", "import Component from 'flarum/Component';\nimport Link from 'flarum/common/components/Link';\nimport avatar from 'flarum/common/helpers/avatar';\nimport username from 'flarum/common/helpers/username';\nimport type Mithril from 'mithril';\nimport type { TransferHistoryListItemProps, UserWithMoney } from '../types';\n\n/**\n * Helper function to get user profile link\n */\nconst getUserLink = (user: UserWithMoney): string => {\n  if (user) {\n    return app.route.user(user);\n  }\n  return '#';\n};\n\n/**\n * Component for displaying a single transfer history item\n *\n * This component renders the details of a money transfer including\n * users involved, amount, date, and notes.\n */\nexport default class TransferHistoryListItem extends Component<TransferHistoryListItemProps> {\n  /**\n   * Render the transfer history item\n   */\n  view(): Mithril.Children {\n    const { transferHistory } = this.attrs;\n    const currentUser = app.session.user;\n    let currentUserID = '';\n    if (currentUser) {\n      currentUserID = currentUser.id();\n    }\n\n    const fromUserID = transferHistory.attribute('from_user_id');\n    const assignedAt = transferHistory.assignedAt();\n    const fromUser = transferHistory.fromUser();\n    const targetUser = transferHistory.targetUser();\n    const transferMoney = transferHistory.transferMoney();\n    const transferNotes = transferHistory.notes();\n    const transferID = transferHistory.id();\n\n    // Determine transfer type and styling\n    const isOutgoing = currentUserID === fromUserID;\n    let transferType = '';\n    let transferTypeClass = '';\n\n    if (isOutgoing) {\n      transferType = app.translator.trans('wusong8899-transfer-money.forum.transfer-money-out');\n      transferTypeClass = 'TransferHistoryItem-outgoing';\n    } else {\n      transferType = app.translator.trans('wusong8899-transfer-money.forum.transfer-money-in');\n      transferTypeClass = 'TransferHistoryItem-incoming';\n    }\n\n    // Format money display\n    const moneyName = app.forum.attribute('antoinefr-money.moneyname') || '[money]';\n    const transferMoneyText = moneyName.replace('[money]', transferMoney.toString());\n\n    // Format notes\n    const transferNotesText = transferNotes || app.translator.trans('wusong8899-transfer-money.forum.transfer-list-transfer-notes-none');\n\n    return (\n      <div className={`TransferHistoryItem ${transferTypeClass}`}>\n        <div className=\"TransferHistoryItem-header\">\n          <span className=\"TransferHistoryItem-type\">\n            <strong>{app.translator.trans('wusong8899-transfer-money.forum.transfer-list-type')}: </strong>\n            <span className={`TransferHistoryItem-typeLabel ${transferTypeClass}`}>\n              {transferType}\n            </span>\n          </span>\n          <span className=\"TransferHistoryItem-separator\"> | </span>\n          <span className=\"TransferHistoryItem-date\">\n            <strong>{app.translator.trans('wusong8899-transfer-money.forum.transfer-list-assign-at')}: </strong>\n            <span className=\"TransferHistoryItem-dateValue\">\n              {assignedAt.toLocaleDateString()} {assignedAt.toLocaleTimeString()}\n            </span>\n          </span>\n        </div>\n\n        <div className=\"TransferHistoryItem-details\">\n          <div className=\"TransferHistoryItem-id\">\n            <strong>{app.translator.trans('wusong8899-transfer-money.forum.transfer-list-id')}: </strong>\n            {transferID}\n          </div>\n\n          <div className=\"TransferHistoryItem-users\">\n            <span className=\"TransferHistoryItem-fromUser\">\n              <strong>{app.translator.trans('wusong8899-transfer-money.forum.transfer-list-from-user')}: </strong>\n              <Link href={getUserLink(fromUser)} className=\"TransferHistoryItem-userLink\">\n                {avatar(fromUser)} {username(fromUser)}\n              </Link>\n            </span>\n            <span className=\"TransferHistoryItem-separator\"> | </span>\n            <span className=\"TransferHistoryItem-targetUser\">\n              <strong>{app.translator.trans('wusong8899-transfer-money.forum.transfer-list-target-user')}: </strong>\n              <Link href={getUserLink(targetUser)} className=\"TransferHistoryItem-userLink\">\n                {avatar(targetUser)} {username(targetUser)}\n              </Link>\n            </span>\n          </div>\n\n          <div className=\"TransferHistoryItem-amount\">\n            <strong>{app.translator.trans('wusong8899-transfer-money.forum.transfer-list-transfer-amount')}: </strong>\n            <span className=\"TransferHistoryItem-money\">{transferMoneyText}</span>\n          </div>\n\n          {transferNotes && (\n            <div className=\"TransferHistoryItem-notes\">\n              <strong>{app.translator.trans('wusong8899-transfer-money.forum.transfer-list-transfer-notes')}: </strong>\n              <span className=\"TransferHistoryItem-notesText\">{transferNotesText}</span>\n            </div>\n          )}\n        </div>\n      </div>\n    );\n  }\n}\n", "import Component from 'flarum/Component';\nimport app from 'flarum/app';\nimport Button from 'flarum/common/components/Button';\nimport LoadingIndicator from 'flarum/common/components/LoadingIndicator';\nimport Placeholder from 'flarum/common/components/Placeholder';\nimport Alert from 'flarum/common/components/Alert';\nimport TransferHistoryListItem from './transfer-history-list-item';\nimport type Mithril from 'mithril';\nimport type {\n  TransferHistoryListProps,\n  TransferMoneyModel,\n  UserWithMoney,\n  ComponentState\n} from '../types';\nimport { STANDARD_ITEMS_PER_PAGE, DEFAULT_MONEY_AMOUNT } from '../constants';\n\n/**\n * Component for displaying a list of transfer history records\n *\n * This component handles loading, displaying, and pagination of\n * money transfer history for a specific user.\n */\nexport default class TransferHistoryList extends Component<TransferHistoryListProps> {\n  private state: ComponentState = {\n    loading: false,\n    error: '',\n    data: [],\n    hasMore: false\n  };\n\n  private user: UserWithMoney;\n  private static readonly ITEMS_PER_PAGE = STANDARD_ITEMS_PER_PAGE; // Standard pagination size\n  private static readonly EMPTY_LIST_LENGTH = DEFAULT_MONEY_AMOUNT;\n\n  /**\n   * Initialize the component and start loading data\n   */\n  oninit(vnode: Mithril.Vnode<TransferHistoryListProps, this>): void {\n    super.oninit(vnode);\n\n    this.user = this.attrs.params.user;\n    this.state = {\n      loading: false,\n      error: '',\n      data: [],\n      hasMore: false\n    };\n\n    this.loadResults();\n  }\n\n  /**\n   * Render the transfer history list\n   */\n  view(): Mithril.Children {\n    // Show loading indicator\n    if (this.state.loading) {\n      return (\n        <div className=\"TransferHistoryList\">\n          <div className=\"TransferHistoryList-loading\">\n            <LoadingIndicator />\n          </div>\n        </div>\n      );\n    }\n\n    // Show error message if there's an error\n    if (this.state.error) {\n      return (\n        <div className=\"TransferHistoryList\">\n          <div className=\"TransferHistoryList-error\">\n            <Alert type=\"error\" dismissible={false}>\n              {this.state.error}\n            </Alert>\n          </div>\n        </div>\n      );\n    }\n\n    // Render transfer history items\n    const transferHistoryItems = this.state.data\n      .filter((transferHistory): transferHistory is TransferMoneyModel =>\n        transferHistory && typeof transferHistory.id === 'function'\n      )\n      .map((transferHistory) => (\n        <li\n          className=\"TransferHistoryList-item\"\n          key={transferHistory.id()}\n          data-id={transferHistory.id()}\n        >\n          <TransferHistoryListItem transferHistory={transferHistory} />\n        </li>\n      ));\n\n    return (\n      <div className=\"TransferHistoryList\">\n        <div className=\"TransferHistoryList-header\">\n          <h3>{app.translator.trans('wusong8899-transfer-money.forum.transfer-history')}</h3>\n        </div>\n\n        <div className=\"TransferHistoryList-content\">\n          {this.state.data.length === TransferHistoryList.EMPTY_LIST_LENGTH && (\n            <div className=\"TransferHistoryList-empty\">\n              <Placeholder text={app.translator.trans('wusong8899-transfer-money.forum.transfer-history-empty')} />\n            </div>\n          )}\n\n          {this.state.data.length > TransferHistoryList.EMPTY_LIST_LENGTH && (\n            <ul className=\"TransferHistoryList-items\">\n              {transferHistoryItems}\n            </ul>\n          )}\n\n          {this.state.hasMore && (\n            <div className=\"TransferHistoryList-loadMore\">\n              <Button\n                className=\"Button\"\n                onclick={this.loadMore.bind(this)}\n                loading={this.state.loading}\n              >\n                {app.translator.trans('wusong8899-transfer-money.forum.load-more')}\n              </Button>\n            </div>\n          )}\n        </div>\n      </div>\n    );\n  }\n\n  /**\n   * Load more transfer history records\n   */\n  loadMore(): void {\n    if (this.state.loading) {\n      return;\n    }\n\n    this.state.loading = true;\n    this.loadResults(this.state.data.length)\n      .catch((error) => this.handleError(error))\n      .finally(() => {\n        this.state.loading = false;\n        m.redraw();\n      });\n  }\n\n  /**\n   * Parse API response and update component state\n   */\n  private parseResults(results: unknown): void {\n    try {\n      if (typeof results === 'object' && results) {\n        const response = results as { payload?: { links?: { next?: string } } };\n        const hasNext = response.payload && response.payload.links && response.payload.links.next;\n        this.state.hasMore = !!hasNext;\n      } else {\n        this.state.hasMore = false;\n      }\n\n      // Get fresh data from store\n      const newData = app.store.all('transferMoney') as TransferMoneyModel[];\n      this.state.data = [...this.state.data, ...newData.filter(item =>\n        !this.state.data.some(existing => existing.id() === item.id())\n      )];\n\n      this.state.error = '';\n    } catch (error) {\n      this.handleError(error);\n    }\n  }\n\n  /**\n   * Handle errors during data loading\n   */\n  private handleError(error: unknown): void {\n    // Log error for debugging (will be removed in production)\n    if (app.forum.attribute('debug')) {\n      // eslint-disable-next-line no-console\n      console.error('Transfer history loading error:', error);\n    }\n\n    if (isApiError(error)) {\n      this.state.error = error.message;\n    } else if (error instanceof Error) {\n      this.state.error = error.message;\n    } else {\n      this.state.error = app.translator.trans('wusong8899-transfer-money.forum.error-loading-history');\n    }\n  }\n\n  /**\n   * Load transfer history results from API\n   */\n  private loadResults(offset?: number): Promise<void> {\n    const DEFAULT_OFFSET = 0;\n    const actualOffset = offset ?? DEFAULT_OFFSET;\n\n    this.state.loading = true;\n    this.state.error = '';\n\n    return app.store.find('transferMoney', {\n      filter: {\n        user: this.user.id(),\n      },\n      page: {\n        offset: actualOffset,\n        limit: TransferHistoryList.ITEMS_PER_PAGE,\n      },\n    })\n    .then((results) => {\n      this.parseResults(results);\n    })\n    .catch((error) => {\n      this.handleError(error);\n      throw error;\n    })\n    .finally(() => {\n      this.state.loading = false;\n    });\n  }\n}\n", "import UserPage from 'flarum/components/UserPage';\nimport TransferHistoryList from './transfer-history-list';\nimport LoadingIndicator from 'flarum/common/components/LoadingIndicator';\nimport type Mithril from 'mithril';\nimport type { UserWithMoney, TransferHistoryPageProps } from '../types';\n\n/**\n * Page component for displaying user's transfer history\n *\n * This component extends UserPage to provide a dedicated view\n * for users to see their money transfer history.\n */\nexport default class TransferHistoryPage extends UserPage<TransferHistoryPageProps> {\n  /**\n   * Initialize the component and load user data\n   */\n  oninit(vnode: Mithril.Vnode<TransferHistoryPageProps, this>): void {\n    super.oninit(vnode);\n\n    const username = m.route.param('username');\n    if (username) {\n      this.loadUser(username);\n    }\n  }\n\n  /**\n   * Render the main content of the transfer history page\n   */\n  content(): JSX.Element {\n    // Show loading indicator while user data is being loaded\n    if (!this.user) {\n      return (\n        <div className=\"TransferHistoryPage\">\n          <div className=\"TransferHistoryPage-loading\">\n            <LoadingIndicator />\n          </div>\n        </div>\n      );\n    }\n\n    const typedUser = this.user as UserWithMoney;\n\n    return (\n      <div className=\"TransferHistoryPage\">\n        <div className=\"TransferHistoryPage-header\">\n          <h2 className=\"TransferHistoryPage-title\">\n            {app.translator.trans('wusong8899-transfer-money.forum.transfer-history')}\n          </h2>\n        </div>\n        <div className=\"TransferHistoryPage-content\">\n          <TransferHistoryList params={{ user: typedUser }} />\n        </div>\n      </div>\n    );\n  }\n\n  /**\n   * Handle user data display\n   */\n  show(user: UserWithMoney): void {\n    super.show(user);\n  }\n\n  /**\n   * Get the page title for SEO and browser tab\n   */\n  title(): string {\n    const baseTitle = app.translator.trans('wusong8899-transfer-money.forum.transfer-history');\n    if (this.user) {\n      return `${baseTitle} - ${this.user.displayName()}`;\n    }\n    return baseTitle;\n  }\n}\n", "import { extend } from 'flarum/common/extend';\nimport LinkButton from 'flarum/components/LinkButton';\nimport UserPage from 'flarum/components/UserPage';\nimport TransferHistoryPage from './components/transfer-history-page';\nimport type ItemList from 'flarum/utils/ItemList';\n\nconst TRANSFER_HISTORY_PRIORITY = 10;\n\nexport default function addTransferMoneyPage(): void {\n  (app as unknown as { routes: Record<string, unknown> }).routes['user.transferHistory'] = {\n    component: TransferHistoryPage,\n    path: '/u/:username/transferHistory',\n  };\n\n  extend(UserPage.prototype, 'navItems', function addNavItem(items: unknown) {\n    if (app.session.user) {\n      const currentUser = app.session.user;\n      const profileUser = this.user;\n\n      if (currentUser && profileUser && currentUser.id() === profileUser.id()) {\n        (items as ItemList<unknown>).add(\n          'transferHistory',\n          LinkButton.component(\n            {\n              href: app.route('user.transferHistory', { username: profileUser.username() }),\n              icon: 'fas fa-exchange-alt',\n            },\n            app.translator.trans('wusong8899-transfer-money.forum.transfer-history')\n          ),\n          TRANSFER_HISTORY_PRIORITY\n        );\n      }\n    }\n  });\n}\n", "import { extend } from \"flarum/extend\";\nimport SessionDropdown from 'flarum/forum/components/SessionDropdown';\nimport TransferMoneyModal from './components/transfer-money-modal';\nimport type { Vnode } from 'mithril';\nimport type User from 'flarum/common/models/User';\nimport app from 'flarum/forum/app';\nimport {\n  CSS_DISPLAY_NONE,\n  CSS_VISIBILITY_HIDDEN,\n  ROUTE_TAGS,\n  CUSTOMIZATION_ENABLED\n} from './constants';\n\nconst CHECK_TIME_INTERVAL = 10;\nconst MIN_ITEM_COUNT = 0;\n\nconst detachTransferMoneyMenu = (): void => {\n  const moneyTransferClient1Customization = app.forum.attribute('moneyTransferClient1Customization');\n\n  if (moneyTransferClient1Customization !== CUSTOMIZATION_ENABLED) {\n    return;\n  }\n\n  const transferMoneyLabelContainer = document.getElementById(\"transferMoneyLabelContainer\");\n\n  if (transferMoneyLabelContainer !== null) {\n    $(transferMoneyLabelContainer).remove();\n    // $(\"#app-navigation\").css(\"height\",\"var(--header-height-phone)\");\n    // $(\"#content .container .IndexPage-results\").css(\"marginTop\",\"15px\");\n  }\n};\n\nconst attachTransferMoneyMenu = (vdom: Vnode<unknown>, _user: User): void => {\n  const isMobileView = $(\"#drawer\").css('visibility') === CSS_VISIBILITY_HIDDEN;\n  const moneyTransferClient1Customization = app.forum.attribute('moneyTransferClient1Customization');\n\n  if (isMobileView === false) { return; }\n  if (moneyTransferClient1Customization !== CUSTOMIZATION_ENABLED) { return; }\n\n  $(\"#content .IndexPage-nav .item-nav\").css(\"display\", CSS_DISPLAY_NONE);\n  $(\"#content .IndexPage-nav .item-newDiscussion\").remove();\n\n  const task = setInterval(function checkDomReady(): void {\n    if (vdom.dom) {\n      clearInterval(task);\n\n      if (vdom.dom) {\n        $(\"#content .IndexPage-nav .item-nav\").css(\"display\", CSS_DISPLAY_NONE);\n        $(\"#content .IndexPage-nav .item-newDiscussion\").remove();\n\n        let transferMoneyLabelContainer = document.getElementById(\"transferMoneyLabelContainer\");\n\n        if (transferMoneyLabelContainer !== null) {\n          return;\n        }\n\n        $(\"#content .IndexPage-nav .item-nav .ButtonGroup\").removeClass(\"App-titleControl\");\n        $(\"#content .IndexPage-nav .item-nav .ButtonGroup button\").addClass(\"Button--link\");\n        let itemNav = $(\"#content .IndexPage-nav .item-nav\").clone();\n\n        if (itemNav.length > MIN_ITEM_COUNT) {\n          $(\"#itemNavClone\").remove();\n          $(itemNav).attr('id', \"itemNavClone\");\n          $(itemNav).css('display', \"\");\n          $(\"#header-secondary .Header-controls\").prepend(itemNav);\n        }\n\n        const appNavigation = document.getElementById(\"app-navigation\");\n        const moneyName = app.forum.attribute('antoinefr-money.moneyname') || '[money]';\n        const userMoneyText = moneyName.replace('[money]', app.session.user.attribute(\"money\"));\n\n        transferMoneyLabelContainer = document.createElement(\"div\");\n        transferMoneyLabelContainer.id = \"transferMoneyLabelContainer\";\n        transferMoneyLabelContainer.className = \"clientCustomizeWithdrawalButtonContainer\";\n\n        const transferMoneyContainer = document.createElement(\"div\");\n        transferMoneyContainer.className = \"clientCustomizeWithdrawalHeaderItems clientCustomizeWithdrawalHeaderTotalMoney\";\n\n        const transferMoneyText = document.createElement(\"div\");\n        transferMoneyText.innerHTML = '<span style=\"font-size:16px;\"><i class=\"fab fa-bitcoin\" style=\"padding-right: 8px;color: gold;\"></i></span>' + userMoneyText;\n        transferMoneyText.className = \"clientCustomizeWithdrawalHeaderText\"\n\n        const transferMoneyIcon = document.createElement(\"div\");\n        transferMoneyIcon.innerHTML = '<i class=\"fas fa-wallet\"></i>';\n        transferMoneyIcon.className = \"clientCustomizeWithdrawalHeaderIcon\";\n\n        transferMoneyContainer.appendChild(transferMoneyText);\n        transferMoneyContainer.appendChild(transferMoneyIcon);\n\n        const transferMoneyButtonText = document.createElement(\"div\");\n        transferMoneyButtonText.innerHTML = app.translator.trans('wusong8899-transfer-money.forum.withdrawal');\n        transferMoneyButtonText.className = \"clientCustomizeWithdrawalHeaderItems clientCustomizeWithdrawalHeaderWithdrawal\";\n\n        $(transferMoneyButtonText).click(function showTransferModal(): void {\n          app.modal.show(TransferMoneyModal);\n        });\n\n        const userAvatarContainer = document.createElement(\"div\");\n        userAvatarContainer.className = \"clientCustomizeWithdrawalHeaderItems clientCustomizeWithdrawalHeaderUser\";\n\n        const avatarClone = $(\"#header-secondary .item-session .SessionDropdown\").clone();\n        $(avatarClone).attr('id', \"avatarClone\");\n        $(avatarClone).addClass(\"App-primaryControl\");\n\n        $(userAvatarContainer).html(avatarClone);\n\n        let hideNavToggle = \"\";\n        $(avatarClone).on('click', function toggleNavigation(): void {\n          if (hideNavToggle === \"\") {\n            hideNavToggle = CSS_DISPLAY_NONE;\n          } else {\n            hideNavToggle = \"\";\n          }\n          $(\"#content .IndexPage-nav\").css(\"display\", hideNavToggle);\n        });\n\n        transferMoneyLabelContainer.appendChild(transferMoneyContainer);\n        transferMoneyLabelContainer.appendChild(transferMoneyButtonText);\n        transferMoneyLabelContainer.appendChild(userAvatarContainer);\n        appNavigation.appendChild(transferMoneyLabelContainer);\n      }\n    }\n  }, CHECK_TIME_INTERVAL);\n};\n\nconst addClient1CustomizationFeatures = (): void => {\n  extend(SessionDropdown.prototype, 'view', function handleSessionDropdownView(vnode): void {\n    if (!app.session.user) {\n      return;\n    }\n\n    const routeName = app.current.get('routeName');\n\n    if (routeName) {\n      if (routeName !== ROUTE_TAGS) {\n        detachTransferMoneyMenu();\n      } else {\n        attachTransferMoneyMenu(vnode, this.attrs.user);\n      }\n    }\n  });\n};\n\nexport default addClient1CustomizationFeatures;\n", "import { extend } from 'flarum/extend';\r\nimport UserControls from 'flarum/utils/UserControls';\r\nimport NotificationGrid from \"flarum/components/NotificationGrid\";\r\nimport SessionDropdown from 'flarum/forum/components/SessionDropdown';\r\nimport Button from 'flarum/components/Button';\r\n\r\nimport TransferMoney from \"./model/transfer-money\";\r\nimport TransferMoneyModal from './components/transfer-money-modal';\r\nimport TransferMoneyNotification from \"./components/transfer-money-notification\";\r\nimport addTransferMoneyPage from \"./add-transfer-money-page\";\r\nimport addClient1CustomizationFeatures from \"./add-client1-customization-features\";\r\n\r\n\r\napp.initializers.add('wusong8899-money-transfer', () => {\r\n  app.store.models.transferMoney = TransferMoney;\r\n  app.notificationComponents.transferMoney = TransferMoneyNotification;\r\n\r\n  addTransferMoneyPage();\r\n  addClient1CustomizationFeatures();\r\n\r\n  extend(NotificationGrid.prototype, \"notificationTypes\", function addTransferMoneyNotification(items) {\r\n    items.add(\"transferMoney\", {\r\n      name: \"transferMoney\",\r\n      icon: \"fas fa-dollar-sign\",\r\n      label: app.translator.trans(\r\n        \"wusong8899-transfer-money.forum.receive-transfer-from-user\"\r\n      ),\r\n    });\r\n  });\r\n\r\n  extend(UserControls, 'moderationControls', (items, user) => {\r\n    const allowUseTransferMoney = app.forum.attribute('allowUseTransferMoney');\r\n\r\n    if (app.session.user && allowUseTransferMoney) {\r\n      const currentUserID = app.session.user.id();\r\n      const targetUserID = user.id();\r\n\r\n      if (currentUserID !== targetUserID) {\r\n        items.add('transferMoney', Button.component({\r\n          icon: 'fas fa-money-bill',\r\n          onclick: () => app.modal.show(TransferMoneyModal, { user })\r\n        }, app.translator.trans('wusong8899-transfer-money.forum.transfer-money'))\r\n        );\r\n      }\r\n    }\r\n  });\r\n\r\n  const TRANSFER_MONEY_PRIORITY = -1;\r\n\r\n  extend(SessionDropdown.prototype, 'items', function addTransferMoneyButton(items) {\r\n    if (!app.session.user) {\r\n      return;\r\n    }\r\n\r\n    items.add(\r\n      'transferMoney',\r\n      Button.component(\r\n        {\r\n          icon: 'fas fa-money-bill',\r\n          onclick: () => {\r\n            app.modal.show(TransferMoneyModal)\r\n          },\r\n        },\r\n        app.translator.trans('wusong8899-transfer-money.forum.transfer-money')\r\n      ),\r\n      TRANSFER_MONEY_PRIORITY\r\n    );\r\n  });\r\n});\r\n"], "names": ["TransferMoney", "Model", "amount", "dateStr", "MIN_QUERY_LENGTH", "SEARCH_LIMIT", "UserSearchSource", "query", "results", "user", "avatar", "username", "highlight", "DEFAULT_MONEY_AMOUNT", "DEFAULT_USER_COUNT", "MINIMUM_TRANSFER_AMOUNT", "DEFAULT_MONEY_NAME", "MONEY_ATTRIBUTE_KEY", "USER_SELECTION_PREFIX", "DEFAULT_USER_SELECTION_COUNT", "RANDOM_STRING_BASE", "RANDOM_STRING_START_INDEX", "SEARCH_TYPING_DELAY", "MIN_SEARCH_LENGTH", "EMPTY_ARRAY_LENGTH", "STANDARD_ITEMS_PER_PAGE", "TEXTAREA_MAX_LENGTH", "BUTTON_WIDTH", "ROUTE_TAGS", "CSS_DISPLAY_NONE", "CSS_VISIBILITY_HIDDEN", "CUSTOMIZATION_ENABLED", "TransferMoneySearchModal", "Search", "vnode", "target", "event", "searchValue", "loading", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app", "recipient", "userName", "userAvatar", "extractText", "classList", "source", "LoadingIndicator", "items", "ItemList", "value", "type", "id", "userID", "inputElement", "store", "_TransferMoneySuccessModal", "Modal", "<PERSON><PERSON>", "TransferMoneySuccessModal", "_TransferMoneyModal", "Stream", "targetUser", "SearchState", "money", "moneyTransferValue", "totalNeedMoney", "totalNeedMoneyText", "needMoneyContainer", "userMoney", "moneyTransferValueTotal", "moneyTransferNotesValue", "<PERSON><PERSON>", "moneyTransferData", "payload", "TransferMoneyModal", "TransferMoneyNotification", "Notification", "notification", "transferMoney", "transferID", "costText", "getUserLink", "TransferHistoryListItem", "Component", "transferHistory", "currentUser", "currentUserID", "fromUserID", "assignedAt", "fromUser", "transferNotes", "isOutgoing", "transferType", "transferTypeClass", "transferMoneyText", "transferNotesText", "Link", "_TransferHistoryList", "transferHistoryItems", "Placeholder", "error", "response", "hasNext", "newData", "item", "existing", "offset", "actualOffset", "TransferHistoryList", "TransferHistoryPage", "UserPage", "typedUser", "baseTitle", "TRANSFER_HISTORY_PRIORITY", "addTransferMoneyPage", "extend", "profileUser", "LinkButton", "CHECK_TIME_INTERVAL", "MIN_ITEM_COUNT", "detachTransferMoneyMenu", "transferMoneyLabelContainer", "attachTransferMoneyMenu", "vdom", "_user", "isMobile<PERSON>iew", "moneyTransferClient1Customization", "task", "itemNav", "appNavigation", "userMoneyText", "transferMoneyContainer", "transferMoneyIcon", "transferMoneyButtonText", "userAvatarContainer", "avatar<PERSON><PERSON>", "hideNavToggle", "addClient1CustomizationFeatures", "SessionDropdown", "routeName", "NotificationGrid", "UserControls", "allowUseTransferMoney", "targetUserID", "TRANSFER_MONEY_PRIORITY"], "mappings": "sFAMA,MAAqBA,UAAsBC,CAAoC,CAI7E,IAAa,CACX,OAAOA,EAAM,UAAkB,IAAI,EAAE,KAAK,IAAI,GAAK,EACrD,CAKA,eAAwB,CAEtB,MAAMC,EAASD,EAAM,UAAkB,sBAAsB,EAAE,KAAK,IAAI,EACxE,OAAIC,IAAW,MAAQ,OAAOA,EAAW,IAChC,EAEFA,CACT,CAKA,OAAuB,CACrB,OAAOD,EAAM,UAAyB,OAAO,EAAE,KAAK,IAAI,CAC1D,CAKA,YAAmB,CACjB,MAAME,EAAUF,EAAM,UAAkB,aAAa,EAAE,KAAK,IAAI,EAChE,OAAIE,EACK,IAAI,KAAKA,CAAO,MAEd,IACb,CAKA,UAA0B,CACxB,OAAOF,EAAM,OAAsB,UAAU,EAAE,KAAK,IAAI,CAC1D,CAKA,YAA4B,CAC1B,OAAOA,EAAM,OAAsB,YAAY,EAAE,KAAK,IAAI,CAC5D,CAKA,WAAkB,CAChB,MAAME,EAAUF,EAAM,UAAkB,YAAY,EAAE,KAAK,IAAI,EAC/D,OAAIE,EACK,IAAI,KAAKA,CAAO,MAEd,IACb,CAKA,WAAkB,CAChB,MAAMA,EAAUF,EAAM,UAAkB,YAAY,EAAE,KAAK,IAAI,EAC/D,OAAIE,EACK,IAAI,KAAKA,CAAO,MAEd,IACb,CACF,CAGA,OAAO,OAAOH,EAAc,UAAW,CAErC,GAAIC,EAAM,UAAkB,IAAI,EAChC,cAAeA,EAAM,UAAkB,sBAAsB,EAC7D,MAAOA,EAAM,UAAyB,OAAO,EAC7C,WAAYA,EAAM,UAAkB,aAAa,EACjD,SAAUA,EAAM,OAAO,UAAU,EACjC,WAAYA,EAAM,OAAO,YAAY,CACvC,CAAC,ECrFD,MAAMG,GAAmB,EACnBC,GAAe,EAErB,MAAqBC,EAAiB,CAAtC,aAAA,CACE,KAAQ,QAAqB,CAAA,CAAC,CAE9B,OAAOC,EAA8B,CACnC,MAAI,CAACA,GAASA,EAAM,OAASH,IAC3B,KAAK,QAAU,CAAA,EACR,QAAQ,QAAA,GAGV,IAAI,MAAM,KAAK,QAAS,CAAE,OAAQ,CAAE,MAAAG,CAAA,EAAS,KAAM,CAAE,MAAOF,EAAA,CAAa,CAAG,EAChF,KAAMG,GAAY,CACjB,KAAK,QAAUA,CACjB,CAAC,CACL,CAEA,KAAKD,EAAgC,CACnC,MAAI,CAAC,KAAK,SAAW,CAAC,KAAK,QAAQ,OAC1B,CAAA,EAGF,KAAK,QAAQ,IAAKE,GACvB,EAAC,MAAG,UAAU,eAAe,aAAY,SAAUA,EAA0B,GAAA,CAAI,IAC/E,EAAC,IAAA,CAAE,UAAU,eAAe,SAAS,IAAA,EAClCC,EAAOD,CAAI,EAAE,IAAEE,EAASF,CAAI,IAC5B,OAAA,CAAK,UAAU,sBAAA,EAAwBG,EAAWH,EAAgC,WAAYF,CAAK,CAAE,CACxG,CACF,CACD,CACH,CACF,CChCO,MAAMM,EAAuB,EACvBC,GAAqB,EACrBC,GAA0B,EAS1BC,GAAqB,UAGrBC,GAAsB,4BAGtBC,GAAwB,SAGxBC,GAA+B,EAG/BC,GAAqB,GACrBC,GAA4B,EAC5BC,GAAsB,IACtBC,GAAoB,EACpBC,EAAqB,EAKrBC,GAA0B,GAG1BC,GAAsB,IACtBC,GAAe,OAIfC,GAAa,OAGbC,EAAmB,OACnBC,GAAwB,SAIxBC,EAAwB,IC3BrC,MAAqBC,WAAiCC,CAAsC,CAA5F,aAAA,CAAA,MAAA,GAAA,SAAA,EAGE,KAAQ,SAAW,EAAA,CAEnB,OAAOC,EAAiE,CACtE,MAAM,OAAOA,CAAK,EAClB,KAAK,UAAY,KAAK,OAAA,EAAS,SAASd,EAAkB,EAAE,UAAUC,EAAyB,CACjG,CAEA,SAASa,EAAoE,CAC3E,MAAM,SAASA,CAAK,EAEpB,KAAK,EAAE,iBAAiB,EAAE,GAAG,QAAS,IAAM,CAC1C,MAAMC,EAAS,KAAK,EAAE,sBAAsB,EAC5C,KAAK,aAAaA,EAAO,KAAK,OAAO,CAAW,EAChD,KAAK,EAAE,kBAAkB,EAAE,MAAA,CAC7B,CAAC,EAED,KAAK,EAAE,iBAAiB,EAAE,GAAG,aAAeC,GAAsB,CAChE,MAAMD,EAAS,KAAK,EAAEC,EAAM,MAAiB,EAAE,OAAA,EAC/C,KAAK,aAAaD,EAAO,KAAK,OAAO,CAAW,EAChD,KAAK,EAAE,kBAAkB,EAAE,MAAA,CAC7B,CAAC,EAEA,WAAwD,OAAO,kBAAkB,EAC/E,GAAG,QAAS,IAAM,CACjB,aAAa,KAAK,WAAW,EAC7B,KAAK,SAAW,GAChB,KAAK,YAAc,WAAW,WAAW,IAAM,CAC7C,KAAK,SAAW,GAChB,EAAE,OAAA,CACJ,EAAGb,EAAmB,CACxB,CAAC,EACA,GAAG,UAAW,IAAM,CACnB,aAAa,KAAK,WAAW,CAC/B,CAAC,EAEH,MAAM,SAASY,CAAK,CACtB,CAEA,MAAyB,CACnB,OAAO,KAAK,YAAY,SAAA,EAAe,KACzC,KAAK,YAAY,SAAS,EAAE,EAG9B,MAAMG,EAAc,KAAK,YAAY,SAAA,EAC/BC,EAAUD,GAAeA,EAAY,QAAUd,GAEhD,KAAK,UACR,KAAK,QAAU,KAAK,YAAA,EAAc,QAAA,GAGpC,MAAMgB,EAAqB,KAAK,MAAM,SAAA,EAAoC,QAAA,EAE1E,OACE,EAAC,MAAA,CAAI,UAAU,yBAAA,EACb,EAAC,MAAA,CAAI,UAAU,iCAAA,EACZA,EAAkB,SAAWf,GAC5B,EAAC,OAAI,MAAM,0CAA0C,MAAM,6BAAA,EACzD,EAAC,OAAA,CAAK,MAAM,sBAAsBgB,EAAI,WAAW,MAAM,kEAAkE,CAAE,CAC7H,EAGDD,EACE,IAAKE,GAAoB,CACxB,MAAMC,EAAW/B,EAAS8B,CAAS,EAC7BE,EAAajC,EAAO+B,CAAS,EAEnC,SACG,MAAA,CAAI,MAAM,8BAA8B,QAAUL,GAAsB,KAAK,gBAAgBK,EAAWL,CAAK,CAAA,IAC3G,OAAA,CAAK,MAAM,sBAAsBO,CAAW,EAAO,IAAED,CACxD,CAEJ,CAAC,CACL,EAEA,EAAC,MAAA,CAAI,UAAU,UACb,EAAC,MAAA,CAAI,UAAU,cAAA,EACb,EAAC,QAAA,CACC,UAAU,8BACV,KAAK,SACL,YAAaE,GAAYJ,EAAI,WAAW,MAAM,6DAA6D,CAAC,EAC5G,MAAO,KAAK,YAAY,SAAA,EACxB,QAAUJ,GAAsB,KAAK,YAAY,SAAUA,EAAM,OAA4B,KAAK,EAClG,QAAS,IAAO,KAAK,SAAW,GAChC,OAAQ,IAAO,KAAK,SAAW,EAAA,CAAA,IAEhC,KAAA,CAAG,UAAWS,GAAU,gBAAiB,iBAAkB,OAAQ,CAAE,GAAI,CAAC,CAACP,EAAS,CAAA,EAClF,KAAK,qBACR,CACF,CACF,CACF,CAEJ,CAEQ,qBAAwC,CAC9C,OAAK,KAAK,SAIH,KAAK,QAAQ,IAAKQ,GAA6BA,EAAO,KAAK,KAAK,YAAY,SAAA,CAAU,CAAC,EAHrFC,EAAiB,UAAU,CAAE,KAAM,OAAQ,UAAW,mCAAoC,CAIrG,CAEA,aAA0C,CACxC,MAAMC,EAAQ,IAAIC,EAClB,OAAAD,EAAM,IAAI,QAAS,IAAI1C,EAAkB,EAClC0C,CACT,CAEA,aAAaE,EAAqB,CAChC,KAAM,CAACC,EAAMC,CAAE,EAAIF,EAAM,MAAM,GAAG,EAC5BT,EAAY,KAAK,cAAcU,EAAMC,CAAE,EAEzCX,IACD,KAAK,MAAM,SAAA,EAAoC,IAAIS,EAAOT,CAAS,EACpE,KAAK,MAAM,cAAcA,EAAU,KAAK,EAAE,EAAI,EAC7C,KAAK,MAAM,UAAsC,KAAK,cAAc,EACrE,KAAK,YAAY,SAAS,EAAE,EAEhC,CAEA,gBAAgBA,EAAiBL,EAAoB,CACnDA,EAAM,eAAA,EAEN,MAAMiB,EAASZ,EAAU,KAAK,GAC9B,QAAQ,eAAe,KAAK,MAAM,cAAeY,CAAM,EAEtD,KAAK,MAAM,SAAA,EAAoC,OAAO,SAAWA,CAAM,EACvE,KAAK,MAAM,UAAsC,KAAK,cAAc,CACvE,CAEA,cAAuB,CACrB,MAAMC,EAAe,SAAS,eAAe,oBAAoB,EACjE,OAAKA,EAGsB,OAAO,WAAWA,EAAa,KAAK,EACnC,OAAO,KAAK,KAAK,MAAM,aAAa,EAAE,OAHzD9B,CAIX,CAEA,cAAc+B,EAAeH,EAAyB,CACpD,OAAOZ,EAAI,MAAM,QAAQe,EAAOH,CAAE,CACpC,CACF,CCrKA,MAAqBI,EAArB,MAAqBA,UAAkCC,CAAM,CAG3D,OAAOvB,EAA2C,CAChD,MAAM,OAAOA,CAAK,CACpB,CAEA,WAAoB,CAClB,MAAO,cACT,CAEA,OAAgB,CACd,OAAOM,EAAI,WAAW,MAAM,wDAAwD,CACtF,CAEA,SAA4B,CAC1B,MAAO,CACL,EAAC,OAAI,UAAU,YAAA,IACZ,MAAA,CAAI,MAAM,qBACRkB,EAAO,UACN,CACE,MAAO,SAAS/B,EAAY,GAC5B,UAAW,yBACX,QAAS,IAAM,CACb,SAAS,OAAA,CACX,CAAA,EAEFa,EAAI,WAAW,MAAM,oCAAoC,CAAA,CAE7D,CACF,CAAA,CAEJ,CACF,EAhCEgB,EAAO,cAAgB,GADzB,IAAqBG,EAArBH,ECoBA,MAAqBI,EAArB,MAAqBA,UAA2BH,CAA+B,CAA/E,aAAA,CAAA,MAAA,GAAA,SAAA,EAkEE,KAAQ,aAAe,IAAY,CACjC,EAAE,OAAA,CACJ,CAAA,CA3DA,OAAOvB,EAA2D,CAChE,MAAM,OAAOA,CAAK,EAElB,KAAK,SAAW2B,EAAO,IAAIZ,CAAU,EACrC,KAAK,cAAgB,CAAA,EACrB,KAAK,UAAYT,EAAI,MAAM,UAAUvB,EAAmB,GAAeD,GAEvE,MAAM8C,EAAa,KAAK,MAAM,KAC1BA,IACF,KAAK,WAAW,IAAI5C,GAAwB4C,EAAW,GAAA,EAAMA,CAAU,EACvE,KAAK,cAAcA,EAAW,GAAA,CAAI,EAAI3C,IAGxC,KAAK,gBAAkB,IAAI4C,EAE3B,KAAK,UAAYF,EAAOhD,CAAoB,CAC9C,CAEA,WAAoB,CAClB,MAAO,cACT,CAEA,OAAgB,CACd,OAAO2B,EAAI,WAAW,MAAM,gDAAgD,CAC9E,CAEQ,qBAA8B,CACpC,GAAI,CAACA,EAAI,QAAQ,KACf,OAAO3B,EAET,MAAMmD,EAAQxB,EAAI,QAAQ,KAAK,UAAU,OAAO,EAChD,OAAI,OAAOwB,GAAU,SACZA,EAEFnD,CACT,CAEQ,4BAAqC,CAC3C,MAAMyC,EAAe,SAAS,eAAe,oBAAoB,EACjE,GAAI,CAACA,EACH,OAAOzC,EAET,MAAMqC,EAAQ,OAAO,WAAWI,EAAa,KAAK,EAClD,OAAI,OAAO,MAAMJ,CAAK,EACbrC,EAEFqC,CACT,CAEQ,4BAAqC,CAC3C,MAAMI,EAAe,SAAS,eAAe,yBAAyB,EACtE,OAAKA,EAGEA,EAAa,MAFX,EAGX,CAMA,SAA4B,CAC1B,OACE,EAAC,MAAA,CAAI,UAAU,YAAA,IACZ,MAAA,CAAI,UAAU,MAAA,EACb,EAAC,OAAI,MAAM,uBAAuB,UAAU,yBAAA,EACzCtB,GAAyB,UAAU,CAClC,MAAO,KAAK,gBACZ,SAAU,KAAK,SACf,cAAe,KAAK,cACpB,UAAW,KAAK,UAChB,SAAU,KAAK,YAAA,CAChB,CACH,EAEA,EAAC,MAAA,CAAI,UAAU,YAAA,EACb,EAAC,QAAA,KACEQ,EAAI,WAAW,MAAM,sDAAsD,EAC3E,KAAK,UAAU,QAAQ,UAAW,OAAO,KAAK,qBAAqB,CAAC,CACvE,EACA,EAAC,QAAA,CACC,GAAG,qBACH,YAAaA,EAAI,WAAW,MAAM,kEAAkE,EACpG,SAAQ,GACR,UAAU,cACV,KAAK,SACL,KAAK,MACL,IAAI,IACJ,QAAS,IAAM,KAAK,qBAAA,CAAqB,CAAA,EAE3C,EAAC,MAAA,CAAI,MAAM,kBAAA,EACRA,EAAI,WAAW,MAAM,mDAAmD,EACzE,EAAC,OAAA,CAAK,GAAG,oBAAA,EAAsB,KAAK,UAAU,QAAQ,UAAW,OAAO,KAAK,UAAA,CAAW,CAAC,CAAE,CAC7F,CACF,IAEC,MAAA,CAAI,UAAU,YAAA,EACb,EAAC,QAAA,KAAOA,EAAI,WAAW,MAAM,sDAAsD,CAAE,EACrF,EAAC,WAAA,CAAS,GAAG,0BAA0B,UAAWd,GAAqB,UAAU,cAAc,CACjG,EAEA,EAAC,MAAA,CAAI,UAAU,aAAa,MAAM,qBAAA,EAC/BgC,EAAO,UACN,CACE,UAAW,yBACX,KAAM,SACN,QAAS,KAAK,OAAA,EAEhBlB,EAAI,WAAW,MAAM,oCAAoC,CAAA,EACzD,IAEDkB,EAAO,UACN,CACE,UAAW,mCACX,QAAS,KAAK,QACd,QAAS,IAAM,CACb,KAAK,KAAA,EAED,KAAK,MAAM,UAAY,OAAO,KAAK,MAAM,UAAa,YACxD,KAAK,MAAM,SAAA,CAEf,CAAA,EAEFlB,EAAI,WAAW,MAAM,wCAAwC,CAAA,CAEjE,CACF,CACF,CAEJ,CAEQ,mBAA4B,CAClC,MAAMc,EAAe,SAAS,eAAe,oBAAoB,EACjE,GAAI,CAACA,EACH,OAAOzC,EAGT,IAAIoD,EAAqB,OAAO,WAAWX,EAAa,KAAK,EAE7D,OAAI,OAAO,MAAMW,CAAkB,IACjCA,EAAqBpD,GAGhB,OAAO,KAAK,KAAK,aAAa,EAAE,OAASoD,CAClD,CAEQ,sBAA6B,CACnC,MAAMC,EAAiB,KAAK,kBAAA,EACtBC,EAAqB,KAAK,UAAU,QAAQ,UAAW,OAAOD,CAAc,CAAC,EAC7EE,EAAqB,SAAS,eAAe,oBAAoB,EACnEA,IACFA,EAAmB,YAAcD,EAErC,CAEA,SAAS/B,EAAoB,CAC3BA,EAAM,eAAA,EACN,MAAMiC,EAAY,KAAK,oBAAA,EACjBJ,EAAqB,KAAK,2BAAA,EAC1BK,EAA0B,KAAK,kBAAA,EAC/BC,EAA0B,KAAK,2BAAA,EAErC,GAAID,EAA0BD,EAAW,CACvC7B,EAAI,OAAO,KAAKgC,EAAO,CAAE,KAAM,OAAA,EAAWhC,EAAI,WAAW,MAAM,kEAAkE,CAAC,EAClI,MACF,CAEA,GAAI,OAAO,KAAK,KAAK,aAAa,EAAE,SAAW1B,GAAoB,CACjE0B,EAAI,OAAO,KACTgC,EACA,CAAE,KAAM,OAAA,EACRhC,EAAI,WAAW,MAAM,wEAAwE,CAAA,EAE/F,MACF,CAEA,GAAIyB,EAAqBlD,GAAyB,CAChD,MAAM0D,EAAoB,CACxB,cAAeR,EACf,mBAAoBM,EACpB,cAAe,KAAK,UAAU,OAAO,KAAK,KAAK,aAAa,CAAC,CAAA,EAG/D,KAAK,QAAU,GAEf/B,EAAI,MACD,aAAa,eAAe,EAC5B,KAAKiC,CAAiB,EACtB,KAAMC,GAA0C,CAC/ClC,EAAI,MAAM,YAAYkC,CAAO,EAC7BlC,EAAI,MAAM,KAAKmB,CAAyB,EACxC,KAAK,QAAU,GAEX,KAAK,MAAM,UAAY,OAAO,KAAK,MAAM,UAAa,YACxD,KAAK,MAAM,SAAA,CAEf,CAAC,EACA,MAAM,IAAM,CACX,KAAK,QAAU,EACjB,CAAC,CACL,CACF,CACF,EAlNEC,EAAO,cAAgB,GADzB,IAAqBe,EAArBf,ECtBA,MAAqBgB,WAAkCC,EAAkD,CACvG,MAAe,CACb,MAAO,mBACT,CAEA,MAAe,CACb,KAAM,CAAE,KAAApE,GAAS+B,EAAI,QACrB,IAAI7B,EAAW,GACf,OAAIF,IACFE,EAAWF,EAAK,SAAA,GAEX+B,EAAI,MAAM,uBAAwB,CAAE,SAAA7B,EAAU,CACvD,CAEA,SAA4B,CAC1B,MAAMF,EAAO,KAAK,MAAM,aAAa,SAAA,EACrC,OAAO+B,EAAI,WAAW,MACpB,2EACA,CACE,KAAA/B,CAAA,CACF,CAEJ,CAEA,SAA4B,CAC1B,MAAMqE,EAAe,KAAK,MAAM,aAAa,QAAA,EAC7C,IAAIC,EAAyB,GACzBC,EAAsB,GAEtBF,GAAiBA,EAA0C,YAC7DC,EAAiBD,EAAyC,UAAU,sBAAsB,EAC1FE,EAAcF,EAAyC,UAAU,IAAI,GAGvE,MAAMG,GADYzC,EAAI,MAAM,UAAU,2BAA2B,GAAK,WAC3C,QAAQ,UAAWuC,CAAa,EAE3D,OAAOvC,EAAI,WAAW,MACpB,4EACA,CACE,KAAMyC,EACN,GAAID,CAAA,CACN,CAEJ,CACF,CCtCA,MAAME,EAAezE,GACfA,EACK,IAAI,MAAM,KAAKA,CAAI,EAErB,IAST,MAAqB0E,WAAgCC,CAAwC,CAI3F,MAAyB,CACvB,KAAM,CAAE,gBAAAC,GAAoB,KAAK,MAC3BC,EAAc,IAAI,QAAQ,KAChC,IAAIC,EAAgB,GAChBD,IACFC,EAAgBD,EAAY,GAAA,GAG9B,MAAME,EAAaH,EAAgB,UAAU,cAAc,EACrDI,EAAaJ,EAAgB,WAAA,EAC7BK,EAAWL,EAAgB,SAAA,EAC3BvB,EAAauB,EAAgB,WAAA,EAC7BN,EAAgBM,EAAgB,cAAA,EAChCM,EAAgBN,EAAgB,MAAA,EAChCL,EAAaK,EAAgB,GAAA,EAG7BO,EAAaL,IAAkBC,EACrC,IAAIK,EAAe,GACfC,EAAoB,GAEpBF,GACFC,EAAe,IAAI,WAAW,MAAM,oDAAoD,EACxFC,EAAoB,iCAEpBD,EAAe,IAAI,WAAW,MAAM,mDAAmD,EACvFC,EAAoB,gCAKtB,MAAMC,GADY,IAAI,MAAM,UAAU,2BAA2B,GAAK,WAClC,QAAQ,UAAWhB,EAAc,UAAU,EAGzEiB,EAAoBL,GAAiB,IAAI,WAAW,MAAM,mEAAmE,EAEnI,OACE,EAAC,OAAI,UAAW,uBAAuBG,CAAiB,EAAA,EACtD,EAAC,OAAI,UAAU,4BAAA,IACZ,OAAA,CAAK,UAAU,4BACd,EAAC,SAAA,KAAQ,IAAI,WAAW,MAAM,oDAAoD,EAAE,IAAE,IACrF,OAAA,CAAK,UAAW,iCAAiCA,CAAiB,EAAA,EAChED,CACH,CACF,IACC,OAAA,CAAK,UAAU,iCAAgC,KAAG,IAClD,OAAA,CAAK,UAAU,4BACd,EAAC,SAAA,KAAQ,IAAI,WAAW,MAAM,yDAAyD,EAAE,IAAE,IAC1F,OAAA,CAAK,UAAU,iCACbJ,EAAW,mBAAA,EAAqB,IAAEA,EAAW,oBAChD,CACF,CACF,EAEA,EAAC,OAAI,UAAU,6BAAA,EACb,EAAC,MAAA,CAAI,UAAU,wBAAA,IACZ,SAAA,KAAQ,IAAI,WAAW,MAAM,kDAAkD,EAAE,IAAE,EACnFT,CACH,EAEA,EAAC,OAAI,UAAU,2BAAA,IACZ,OAAA,CAAK,UAAU,gCACd,EAAC,SAAA,KAAQ,IAAI,WAAW,MAAM,yDAAyD,EAAE,IAAE,IAC1FiB,EAAA,CAAK,KAAMf,EAAYQ,CAAQ,EAAG,UAAU,8BAAA,EAC1ChF,EAAOgF,CAAQ,EAAE,IAAE/E,EAAS+E,CAAQ,CACvC,CACF,EACA,EAAC,OAAA,CAAK,UAAU,+BAAA,EAAgC,KAAG,EACnD,EAAC,OAAA,CAAK,UAAU,gCAAA,EACd,EAAC,cAAQ,IAAI,WAAW,MAAM,2DAA2D,EAAE,IAAE,EAC7F,EAACO,GAAK,KAAMf,EAAYpB,CAAU,EAAG,UAAU,8BAAA,EAC5CpD,EAAOoD,CAAU,EAAE,IAAEnD,EAASmD,CAAU,CAC3C,CACF,CACF,EAEA,EAAC,MAAA,CAAI,UAAU,4BAAA,EACb,EAAC,cAAQ,IAAI,WAAW,MAAM,+DAA+D,EAAE,IAAE,EACjG,EAAC,OAAA,CAAK,UAAU,6BAA6BiC,CAAkB,CACjE,EAECJ,KACE,MAAA,CAAI,UAAU,6BACb,EAAC,SAAA,KAAQ,IAAI,WAAW,MAAM,8DAA8D,EAAE,IAAE,EAChG,EAAC,OAAA,CAAK,UAAU,+BAAA,EAAiCK,CAAkB,CACrE,CAEJ,CACF,CAEJ,CACF,CChGA,MAAqBE,EAArB,MAAqBA,UAA4Bd,CAAoC,CAArF,aAAA,CAAA,MAAA,GAAA,SAAA,EACE,KAAQ,MAAwB,CAC9B,QAAS,GACT,MAAO,GACP,KAAM,CAAA,EACN,QAAS,EAAA,CACX,CASA,OAAOlD,EAA4D,CACjE,MAAM,OAAOA,CAAK,EAElB,KAAK,KAAO,KAAK,MAAM,OAAO,KAC9B,KAAK,MAAQ,CACX,QAAS,GACT,MAAO,GACP,KAAM,CAAA,EACN,QAAS,EAAA,EAGX,KAAK,YAAA,CACP,CAKA,MAAyB,CAEvB,GAAI,KAAK,MAAM,QACb,OACE,EAAC,MAAA,CAAI,UAAU,qBAAA,EACb,EAAC,MAAA,CAAI,UAAU,6BAAA,EACb,EAACa,EAAA,IAAiB,CACpB,CACF,EAKJ,GAAI,KAAK,MAAM,MACb,SACG,MAAA,CAAI,UAAU,uBACb,EAAC,MAAA,CAAI,UAAU,2BAAA,EACb,EAACyB,EAAA,CAAM,KAAK,QAAQ,YAAa,EAAA,EAC9B,KAAK,MAAM,KACd,CACF,CACF,EAKJ,MAAM2B,EAAuB,KAAK,MAAM,KACrC,OAAQd,GACPA,GAAmB,OAAOA,EAAgB,IAAO,UAAA,EAElD,IAAKA,GACJ,EAAC,KAAA,CACC,UAAU,2BACV,IAAKA,EAAgB,GAAA,EACrB,UAASA,EAAgB,GAAA,CAAG,EAE5B,EAACF,IAAwB,gBAAAE,CAAA,CAAkC,CAAA,CAE9D,EAEH,OACE,EAAC,MAAA,CAAI,UAAU,qBAAA,EACb,EAAC,MAAA,CAAI,UAAU,4BAAA,EACb,EAAC,KAAA,KAAI7C,EAAI,WAAW,MAAM,kDAAkD,CAAE,CAChF,EAEA,EAAC,MAAA,CAAI,UAAU,6BAAA,EACZ,KAAK,MAAM,KAAK,SAAW0D,EAAoB,mBAC9C,EAAC,MAAA,CAAI,UAAU,2BAAA,EACb,EAACE,GAAA,CAAY,KAAM5D,EAAI,WAAW,MAAM,wDAAwD,CAAA,CAAG,CACrG,EAGD,KAAK,MAAM,KAAK,OAAS0D,EAAoB,mBAC5C,EAAC,KAAA,CAAG,UAAU,2BAAA,EACXC,CACH,EAGD,KAAK,MAAM,SACV,EAAC,MAAA,CAAI,UAAU,8BAAA,EACb,EAACzC,EAAA,CACC,UAAU,SACV,QAAS,KAAK,SAAS,KAAK,IAAI,EAChC,QAAS,KAAK,MAAM,OAAA,EAEnBlB,EAAI,WAAW,MAAM,2CAA2C,CAAA,CAErE,CAEJ,CACF,CAEJ,CAKA,UAAiB,CACX,KAAK,MAAM,UAIf,KAAK,MAAM,QAAU,GACrB,KAAK,YAAY,KAAK,MAAM,KAAK,MAAM,EACpC,MAAO6D,GAAU,KAAK,YAAYA,CAAK,CAAC,EACxC,QAAQ,IAAM,CACb,KAAK,MAAM,QAAU,GACrB,EAAE,OAAA,CACJ,CAAC,EACL,CAKQ,aAAa7F,EAAwB,CAC3C,GAAI,CACF,GAAI,OAAOA,GAAY,UAAYA,EAAS,CAC1C,MAAM8F,EAAW9F,EACX+F,EAAUD,EAAS,SAAWA,EAAS,QAAQ,OAASA,EAAS,QAAQ,MAAM,KACrF,KAAK,MAAM,QAAU,CAAC,CAACC,CACzB,MACE,KAAK,MAAM,QAAU,GAIvB,MAAMC,EAAUhE,EAAI,MAAM,IAAI,eAAe,EAC7C,KAAK,MAAM,KAAO,CAAC,GAAG,KAAK,MAAM,KAAM,GAAGgE,EAAQ,OAAOC,GACvD,CAAC,KAAK,MAAM,KAAK,KAAKC,GAAYA,EAAS,OAASD,EAAK,GAAA,CAAI,CAAA,CAC9D,EAED,KAAK,MAAM,MAAQ,EACrB,OAASJ,EAAO,CACd,KAAK,YAAYA,CAAK,CACxB,CACF,CAKQ,YAAYA,EAAsB,CAEpC7D,EAAI,MAAM,UAAU,OAAO,GAE7B,QAAQ,MAAM,kCAAmC6D,CAAK,EAGpD,WAAWA,CAAK,EAClB,KAAK,MAAM,MAAQA,EAAM,QAChBA,aAAiB,MAC1B,KAAK,MAAM,MAAQA,EAAM,QAEzB,KAAK,MAAM,MAAQ7D,EAAI,WAAW,MAAM,uDAAuD,CAEnG,CAKQ,YAAYmE,EAAgC,CAElD,MAAMC,EAAeD,GAAU,EAE/B,YAAK,MAAM,QAAU,GACrB,KAAK,MAAM,MAAQ,GAEZnE,EAAI,MAAM,KAAK,gBAAiB,CACrC,OAAQ,CACN,KAAM,KAAK,KAAK,GAAA,CAAG,EAErB,KAAM,CACJ,OAAQoE,EACR,MAAOV,EAAoB,cAAA,CAC7B,CACD,EACA,KAAM1F,GAAY,CACjB,KAAK,aAAaA,CAAO,CAC3B,CAAC,EACA,MAAO6F,GAAU,CAChB,WAAK,YAAYA,CAAK,EAChBA,CACR,CAAC,EACA,QAAQ,IAAM,CACb,KAAK,MAAM,QAAU,EACvB,CAAC,CACH,CACF,EA7LEH,EAAwB,eAAiBzE,GACzCyE,EAAwB,kBAAoBrF,EAV9C,IAAqBgG,EAArBX,ECVA,MAAqBY,WAA4BC,CAAmC,CAIlF,OAAO7E,EAA4D,CACjE,MAAM,OAAOA,CAAK,EAElB,MAAMvB,EAAW,EAAE,MAAM,MAAM,UAAU,EACrCA,GACF,KAAK,SAASA,CAAQ,CAE1B,CAKA,SAAuB,CAErB,GAAI,CAAC,KAAK,KACR,OACE,EAAC,MAAA,CAAI,UAAU,qBAAA,EACb,EAAC,MAAA,CAAI,UAAU,6BAAA,EACb,EAACoC,EAAA,IAAiB,CACpB,CACF,EAIJ,MAAMiE,EAAY,KAAK,KAEvB,OACE,EAAC,MAAA,CAAI,UAAU,qBAAA,EACb,EAAC,MAAA,CAAI,UAAU,4BAAA,EACb,EAAC,KAAA,CAAG,UAAU,2BAAA,EACX,IAAI,WAAW,MAAM,kDAAkD,CAC1E,CACF,EACA,EAAC,MAAA,CAAI,UAAU,6BAAA,EACb,EAACH,EAAA,CAAoB,OAAQ,CAAE,KAAMG,CAAA,CAAU,CAAG,CACpD,CACF,CAEJ,CAKA,KAAKvG,EAA2B,CAC9B,MAAM,KAAKA,CAAI,CACjB,CAKA,OAAgB,CACd,MAAMwG,EAAY,IAAI,WAAW,MAAM,kDAAkD,EACzF,OAAI,KAAK,KACA,GAAGA,CAAS,MAAM,KAAK,KAAK,aAAa,GAE3CA,CACT,CACF,CCnEA,MAAMC,GAA4B,GAElC,SAAwBC,IAA6B,CAClD,IAAuD,OAAO,sBAAsB,EAAI,CACvF,UAAWL,GACX,KAAM,8BAAA,EAGRM,GAAAA,OAAOL,EAAS,UAAW,WAAY,SAAoB/D,EAAgB,CACzE,GAAI,IAAI,QAAQ,KAAM,CACpB,MAAMsC,EAAc,IAAI,QAAQ,KAC1B+B,EAAc,KAAK,KAErB/B,GAAe+B,GAAe/B,EAAY,OAAS+B,EAAY,MAChErE,EAA4B,IAC3B,kBACAsE,GAAW,UACT,CACE,KAAM,IAAI,MAAM,uBAAwB,CAAE,SAAUD,EAAY,SAAA,EAAY,EAC5E,KAAM,qBAAA,EAER,IAAI,WAAW,MAAM,kDAAkD,CAAA,EAEzEH,EAAA,CAGN,CACF,CAAC,CACH,CCrBA,MAAMK,GAAsB,GACtBC,GAAiB,EAEjBC,GAA0B,IAAY,CAG1C,GAF0CjF,EAAI,MAAM,UAAU,mCAAmC,IAEvDT,EACxC,OAGF,MAAM2F,EAA8B,SAAS,eAAe,6BAA6B,EAErFA,IAAgC,MAClC,EAAEA,CAA2B,EAAE,OAAA,CAInC,EAEMC,GAA0B,CAACC,EAAsBC,IAAsB,CAC3E,MAAMC,EAAe,EAAE,SAAS,EAAE,IAAI,YAAY,IAAMhG,GAClDiG,EAAoCvF,EAAI,MAAM,UAAU,mCAAmC,EAGjG,GADIsF,IAAiB,IACjBC,IAAsChG,EAAyB,OAEnE,EAAE,mCAAmC,EAAE,IAAI,UAAWF,CAAgB,EACtE,EAAE,6CAA6C,EAAE,OAAA,EAEjD,MAAMmG,EAAO,YAAY,UAA+B,CACtD,GAAIJ,EAAK,MACP,cAAcI,CAAI,EAEdJ,EAAK,KAAK,CACZ,EAAE,mCAAmC,EAAE,IAAI,UAAW/F,CAAgB,EACtE,EAAE,6CAA6C,EAAE,OAAA,EAEjD,IAAI6F,EAA8B,SAAS,eAAe,6BAA6B,EAEvF,GAAIA,IAAgC,KAClC,OAGF,EAAE,gDAAgD,EAAE,YAAY,kBAAkB,EAClF,EAAE,uDAAuD,EAAE,SAAS,cAAc,EAClF,IAAIO,EAAU,EAAE,mCAAmC,EAAE,MAAA,EAEjDA,EAAQ,OAAST,KACnB,EAAE,eAAe,EAAE,OAAA,EACnB,EAAES,CAAO,EAAE,KAAK,KAAM,cAAc,EACpC,EAAEA,CAAO,EAAE,IAAI,UAAW,EAAE,EAC5B,EAAE,oCAAoC,EAAE,QAAQA,CAAO,GAGzD,MAAMC,EAAgB,SAAS,eAAe,gBAAgB,EAExDC,GADY3F,EAAI,MAAM,UAAU,2BAA2B,GAAK,WACtC,QAAQ,UAAWA,EAAI,QAAQ,KAAK,UAAU,OAAO,CAAC,EAEtFkF,EAA8B,SAAS,cAAc,KAAK,EAC1DA,EAA4B,GAAK,8BACjCA,EAA4B,UAAY,2CAExC,MAAMU,EAAyB,SAAS,cAAc,KAAK,EAC3DA,EAAuB,UAAY,iFAEnC,MAAMrC,EAAoB,SAAS,cAAc,KAAK,EACtDA,EAAkB,UAAY,8GAAgHoC,EAC9IpC,EAAkB,UAAY,sCAE9B,MAAMsC,EAAoB,SAAS,cAAc,KAAK,EACtDA,EAAkB,UAAY,gCAC9BA,EAAkB,UAAY,sCAE9BD,EAAuB,YAAYrC,CAAiB,EACpDqC,EAAuB,YAAYC,CAAiB,EAEpD,MAAMC,EAA0B,SAAS,cAAc,KAAK,EAC5DA,EAAwB,UAAY9F,EAAI,WAAW,MAAM,4CAA4C,EACrG8F,EAAwB,UAAY,iFAEpC,EAAEA,CAAuB,EAAE,MAAM,UAAmC,CAClE9F,EAAI,MAAM,KAAKmC,CAAkB,CACnC,CAAC,EAED,MAAM4D,EAAsB,SAAS,cAAc,KAAK,EACxDA,EAAoB,UAAY,2EAEhC,MAAMC,EAAc,EAAE,kDAAkD,EAAE,MAAA,EAC1E,EAAEA,CAAW,EAAE,KAAK,KAAM,aAAa,EACvC,EAAEA,CAAW,EAAE,SAAS,oBAAoB,EAE5C,EAAED,CAAmB,EAAE,KAAKC,CAAW,EAEvC,IAAIC,EAAgB,GACpB,EAAED,CAAW,EAAE,GAAG,QAAS,UAAkC,CACvDC,IAAkB,GACpBA,EAAgB5G,EAEhB4G,EAAgB,GAElB,EAAE,yBAAyB,EAAE,IAAI,UAAWA,CAAa,CAC3D,CAAC,EAEDf,EAA4B,YAAYU,CAAsB,EAC9DV,EAA4B,YAAYY,CAAuB,EAC/DZ,EAA4B,YAAYa,CAAmB,EAC3DL,EAAc,YAAYR,CAA2B,CACvD,CAEJ,EAAGH,EAAmB,CACxB,EAEMmB,GAAkC,IAAY,CAClDtB,EAAAA,OAAOuB,EAAgB,UAAW,OAAQ,SAAmCzG,EAAa,CACxF,GAAI,CAACM,EAAI,QAAQ,KACf,OAGF,MAAMoG,EAAYpG,EAAI,QAAQ,IAAI,WAAW,EAEzCoG,IACEA,IAAchH,GAChB6F,GAAA,EAEAE,GAAwBzF,EAAO,KAAK,MAAM,IAAI,EAGpD,CAAC,CACH,EChIA,IAAI,aAAa,IAAI,4BAA6B,IAAM,CACtD,IAAI,MAAM,OAAO,cAAgBlC,EACjC,IAAI,uBAAuB,cAAgB4E,GAE3CuC,GAAA,EACAuB,GAAA,EAEAtB,EAAAA,OAAOyB,EAAiB,UAAW,oBAAqB,SAAsC7F,EAAO,CACnGA,EAAM,IAAI,gBAAiB,CACzB,KAAM,gBACN,KAAM,qBACN,MAAO,IAAI,WAAW,MACpB,4DAAA,CACF,CACD,CACH,CAAC,EAEDoE,EAAAA,OAAO0B,EAAc,qBAAsB,CAAC9F,EAAOvC,IAAS,CAC1D,MAAMsI,EAAwB,IAAI,MAAM,UAAU,uBAAuB,EAEzE,GAAI,IAAI,QAAQ,MAAQA,EAAuB,CAC7C,MAAMxD,EAAgB,IAAI,QAAQ,KAAK,GAAA,EACjCyD,EAAevI,EAAK,GAAA,EAEtB8E,IAAkByD,GACpBhG,EAAM,IAAI,gBAAiBU,EAAO,UAAU,CAC1C,KAAM,oBACN,QAAS,IAAM,IAAI,MAAM,KAAKiB,EAAoB,CAAE,KAAAlE,EAAM,CAAA,EACzD,IAAI,WAAW,MAAM,gDAAgD,CAAC,CAAA,CAG7E,CACF,CAAC,EAED,MAAMwI,EAA0B,GAEhC7B,EAAAA,OAAOuB,EAAgB,UAAW,QAAS,SAAgC3F,EAAO,CAC3E,IAAI,QAAQ,MAIjBA,EAAM,IACJ,gBACAU,EAAO,UACL,CACE,KAAM,oBACN,QAAS,IAAM,CACb,IAAI,MAAM,KAAKiB,CAAkB,CACnC,CAAA,EAEF,IAAI,WAAW,MAAM,gDAAgD,CAAA,EAEvEsE,CAAA,CAEJ,CAAC,CACH,CAAC"}