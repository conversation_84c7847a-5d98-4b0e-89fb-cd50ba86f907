(function(m,C,p,u){"use strict";class b extends u{}Object.assign(b.prototype,{id:u.attribute("id"),name:u.attribute("name"),icon:u.attribute("icon"),color:u.attribute("color"),url:u.attribute("url"),sort:u.attribute("sort")});class c{constructor(){this.buttonsCustomizationListLoading=!1,this.buttonsCustomizationList=null}static getInstance(){return c.instance||(c.instance=new c),c.instance}async loadButtonsCustomizationList(){if(this.buttonsCustomizationListLoading)return this.waitForButtonsCustomizationList();if(this.buttonsCustomizationList!==null)return this.buttonsCustomizationList;this.buttonsCustomizationListLoading=!0;try{const t=await m.store.find("buttonsCustomizationList").catch(()=>[]);return this.buttonsCustomizationList=[],Array.isArray(t)?this.buttonsCustomizationList.push(...t):t&&this.buttonsCustomizationList.push(t),this.buttonsCustomizationList}catch{return this.buttonsCustomizationList=[],this.buttonsCustomizationList}finally{this.buttonsCustomizationListLoading=!1}}getButtonsCustomizationList(){return this.buttonsCustomizationList}clearCache(){this.buttonsCustomizationList=null}async refreshButtonsCustomizationList(){return this.clearCache(),this.loadButtonsCustomizationList()}isLoading(){return this.buttonsCustomizationListLoading}hasData(){return this.buttonsCustomizationList!==null&&this.buttonsCustomizationList.length>0}getDataCount(){return this.buttonsCustomizationList?this.buttonsCustomizationList.length:0}async forceReload(){return this.buttonsCustomizationListLoading=!1,this.clearCache(),this.loadButtonsCustomizationList()}getButtonById(t){return this.buttonsCustomizationList&&this.buttonsCustomizationList.find(e=>e.id&&e.id()===t)||null}getSortedButtons(){return this.buttonsCustomizationList?[...this.buttonsCustomizationList].sort((t,e)=>{const n=t.sort?t.sort():0,i=e.sort?e.sort():0;return n-i}):[]}async waitForButtonsCustomizationList(){return new Promise(t=>{const e=setInterval(()=>{!this.buttonsCustomizationListLoading&&this.buttonsCustomizationList!==null&&(clearInterval(e),t(this.buttonsCustomizationList))},100);setTimeout(()=>{clearInterval(e),t(this.buttonsCustomizationList||[])},1e4)})}}class s{static createElement(t,e={},n=""){const i=document.createElement(t);return Object.entries(e).forEach(([o,r])=>{o==="className"?i.className=r:o==="style"?i.setAttribute("style",r):i.setAttribute(o,r)}),n&&(i.innerHTML=n),i}static getElementById(t){return document.getElementById(t)}static querySelector(t,e=document){try{return e.querySelector(t)}catch(n){return console.warn(`Invalid selector: ${t}`,n),null}}static querySelectorAll(t,e=document){try{return e.querySelectorAll(t)}catch(n){return console.warn(`Invalid selector: ${t}`,n),document.querySelectorAll("")}}static addEventListener(t,e,n,i=!1){try{t.addEventListener(e,n,i)}catch(o){console.error("Failed to add event listener:",o)}}static removeEventListener(t,e,n,i=!1){try{t.removeEventListener(e,n,i)}catch(o){console.error("Failed to remove event listener:",o)}}static setStyles(t,e){Object.entries(e).forEach(([n,i])=>{try{t.style.setProperty(n,i)}catch(o){console.warn(`Failed to set style ${n}: ${i}`,o)}})}static appendChild(t,e){try{t.appendChild(e)}catch(n){console.error("Failed to append child element:",n)}}static prependChild(t,e){try{t.insertBefore(e,t.firstChild)}catch(n){console.error("Failed to prepend child element:",n)}}static removeElement(t){try{t&&t.parentNode&&t.parentNode.removeChild(t)}catch(e){console.error("Failed to remove element:",e)}}static isVisible(t){try{const e=t;return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)}catch{return!1}}static getDimensions(t){try{const e=t;return{width:e.offsetWidth,height:e.offsetHeight}}catch{return{width:0,height:0}}}static waitForElement(t,e=5e3){return new Promise((n,i)=>{const o=this.querySelector(t);if(o){n(o);return}const r=new MutationObserver(()=>{const d=this.querySelector(t);d&&(r.disconnect(),n(d))});r.observe(document.body,{childList:!0,subtree:!0}),setTimeout(()=>{r.disconnect(),i(new Error(`Element ${t} not found within ${e}ms`))},e)})}}const g=class g{static isMobileDevice(){if(this.isMobile!==null)return this.isMobile;let t=!1;const e=navigator.userAgent||navigator.vendor||window.opera,n=/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i,i=/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw-(n|u)|c55\/|capi|ccwa|cdm-|cell|chtm|cldc|cmd-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc-s|devi|dica|dmob|do(c|p)o|ds(12|-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(-|_)|g1 u|g560|gene|gf-5|g-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd-(m|p|t)|hei-|hi(pt|ta)|hp( i|ip)|hs-c|ht(c(-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i-(20|go|ma)|i230|iac( |-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|-[a-w])|libw|lynx|m1-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|-([1-8]|c))|phil|pire|pl(ay|uc)|pn-2|po(ck|rt|se)|prox|psio|pt-g|qa-a|qc(07|12|21|32|60|-[2-7]|i-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h-|oo|p-)|sdk\/|se(c(-|0|1)|47|mc|nd|ri)|sgh-|shar|sie(-|m)|sk-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h-|v-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl-|tdg-|tel(i|m)|tim-|t-mo|to(pl|sh)|ts(70|m-|m3|m5)|tx-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas-|your|zeto|zte-/i;return(n.test(e)||i.test(e.substr(0,4)))&&(t=!0),this.isMobile=t,t}static getEventType(){return this.isMobileDevice()?"touchend":"click"}static getButtonConfig(){const t=this.isMobileDevice();return{buttonSize:t?"small":"medium",spacing:t?8:12,fontSize:t?"14px":"16px",padding:t?"8px 12px":"10px 16px",maxButtonsPerRow:t?2:4}}static getLayoutConfig(){const t=this.isMobileDevice();return{containerWidth:t?"100%":"auto",containerPadding:t?"10px":"15px",buttonMargin:t?"5px":"8px",flexDirection:t?"column":"row",alignItems:t?"stretch":"center"}}static isTouchDevice(){return"ontouchstart"in window||navigator.maxTouchPoints>0}static getScreenSize(){const t=window.innerWidth;return t<576?"xs":t<768?"sm":t<992?"md":t<1200?"lg":"xl"}static getBreakpoints(){return{xs:{max:575,buttonsPerRow:1,fontSize:"12px"},sm:{min:576,max:767,buttonsPerRow:2,fontSize:"13px"},md:{min:768,max:991,buttonsPerRow:3,fontSize:"14px"},lg:{min:992,max:1199,buttonsPerRow:4,fontSize:"15px"},xl:{min:1200,buttonsPerRow:5,fontSize:"16px"}}}static getCurrentBreakpoint(){const t=this.getScreenSize();return this.getBreakpoints()[t]}};g.isMobile=null;let h=g;const f={app:{extensionId:"wusong8899-client1-buttons-customization",name:"Client1 Buttons Customization",version:"1.0.0"},api:{endpoints:{buttonsList:"/api/buttonsCustomizationList",buttonAdd:"/api/buttonsCustomizationList",buttonUpdate:"/api/buttonsCustomizationList/{id}",buttonDelete:"/api/buttonsCustomizationList/{id}",buttonSort:"/api/buttonsCustomizationList/order"}},data:{apiResources:{buttonsCustomizationList:"buttonsCustomizationList"}},ui:{containerIds:{buttonsContainer:"buttonCustomizationContainer",buttonsWrapper:"buttons-customization-wrapper"},classes:{container:"buttonCustomizationContainer",wrapper:"buttons-customization-wrapper",header:"buttons-customization-header",buttonItem:"custom-button-item",buttonLink:"custom-button-link",buttonContent:"custom-button-content",buttonText:"custom-button-text"},animation:{duration:300,easing:"ease",fadeInDelay:100}},layout:{responsive:{xs:{buttonsPerRow:1,fontSize:"12px",padding:"6px 10px"},sm:{buttonsPerRow:2,fontSize:"13px",padding:"7px 12px"},md:{buttonsPerRow:3,fontSize:"14px",padding:"8px 14px"},lg:{buttonsPerRow:4,fontSize:"15px",padding:"9px 16px"},xl:{buttonsPerRow:5,fontSize:"16px",padding:"10px 18px"}},spacing:{containerPadding:"15px",buttonMargin:"8px",iconMargin:"8px"}},behavior:{autoRefresh:!0,refreshInterval:3e5,loadTimeout:1e4,retryAttempts:3,retryDelay:1e3},styling:{defaultColors:{primary:"#007bff",secondary:"#6c757d",success:"#28a745",danger:"#dc3545",warning:"#ffc107",info:"#17a2b8",light:"#f8f9fa",dark:"#343a40"},buttonStyles:{borderRadius:"6px",transition:"all 0.2s ease",fontWeight:"500",textDecoration:"none",display:"inline-flex",alignItems:"center",justifyContent:"center"}},accessibility:{enableKeyboardNavigation:!0,enableScreenReader:!0,focusOutlineColor:"#007bff",highContrastMode:!1},performance:{enableVirtualization:!1,lazyLoading:!0,debounceDelay:250,cacheTimeout:6e5},debug:{enableLogging:!1,logLevel:"warn",enablePerformanceMonitoring:!1}};class l{constructor(){this.overrides={},this.config={...f}}static getInstance(){return l.instance||(l.instance=new l),l.instance}getConfig(){return this.mergeConfig(this.config,this.overrides)}getSection(t){return this.getConfig()[t]}setOverrides(t){this.overrides={...this.overrides,...t}}getApiResource(){return this.getSection("data").apiResources.buttonsCustomizationList}getApiEndpoint(t){return this.getSection("api").endpoints[t]}getUIClass(t){return this.getSection("ui").classes[t]}getContainerId(t){return this.getSection("ui").containerIds[t]}getResponsiveConfig(){const t=this.getCurrentScreenSize();return this.getSection("layout").responsive[t]}getCurrentScreenSize(){const t=window.innerWidth;return t<576?"xs":t<768?"sm":t<992?"md":t<1200?"lg":"xl"}getBehaviorSetting(t){return this.getSection("behavior")[t]}getStyleSetting(t){return this.getSection("styling")[t]}isDebugEnabled(){return this.getSection("debug").enableLogging}getLogLevel(){return this.getSection("debug").logLevel}validateConfig(){const t=[],e=this.getConfig();return e.app.extensionId||t.push("Extension ID is required"),e.data.apiResources.buttonsCustomizationList||t.push("API resource name is required"),e.behavior.refreshInterval<1e3&&t.push("Refresh interval must be at least 1000ms"),e.behavior.loadTimeout<1e3&&t.push("Load timeout must be at least 1000ms"),e.behavior.retryAttempts<0&&t.push("Retry attempts must be non-negative"),{isValid:t.length===0,errors:t}}resetToDefaults(){this.config={...f},this.overrides={}}mergeConfig(t,e){const n={...t};for(const i in e)if(e.hasOwnProperty(i)){const o=e[i];o&&typeof o=="object"&&!Array.isArray(o)?n[i]={...n[i],...o}:n[i]=o}return n}exportConfig(){return JSON.stringify(this.getConfig(),null,2)}importConfig(t){try{const e=JSON.parse(t);this.setOverrides(e);const n=this.validateConfig();return n.isValid?{success:!0}:{success:!1,error:`Configuration validation failed: ${n.errors.join(", ")}`}}catch(e){return{success:!1,error:`Failed to parse configuration: ${e}`}}}}class y{constructor(){this.dataLoader=c.getInstance(),this.configManager=l.getInstance(),this.containerId=this.configManager.getContainerId("buttonsContainer")}async initialize(){await this.addButtonsCustomization()}async addButtonsCustomization(){const t=await this.dataLoader.loadButtonsCustomizationList();if(s.getElementById(this.containerId)||t.length===0)return;const e=this.createButtonsContainer(),n=this.createButtonsWrapper(e);this.populateButtons(n,t),this.attachToDOM(e)}createButtonsContainer(){const t=s.createElement("div",{id:this.containerId,className:"buttonCustomizationContainer"}),e=s.createElement("div",{className:"buttons-customization-header"},"自定义按钮");return s.appendChild(t,e),t}createButtonsWrapper(t){const e=s.createElement("div",{className:"buttons-customization-wrapper"});return s.appendChild(t,e),e}populateButtons(t,e){this.dataLoader.getSortedButtons().forEach(i=>{const o=this.createButton(i);s.appendChild(t,o)})}createButton(t){const e=t.name?t.name():"Button",n=t.url?t.url():"#",i=t.icon?t.icon():"",o=t.color?t.color():"",r=s.createElement("div",{className:"custom-button-item"}),d=this.createButtonContent(e,n,i,o);return r.innerHTML=d,this.addButtonClickEvent(r,n),r}createButtonContent(t,e,n,i){const o=n?`<i class="${n}" style="margin-right: 8px;"></i>`:"",r=i?`color: ${i};`:"";return`
            <a href="${e}" target="_blank" class="custom-button-link" style="${r}">
                <div class="custom-button-content">
                    ${o}
                    <span class="custom-button-text">${t}</span>
                </div>
            </a>
        `}addButtonClickEvent(t,e){s.addEventListener(t,h.getEventType(),n=>{n.preventDefault(),e&&e!=="#"&&window.open(e,"_blank")})}attachToDOM(t){const e=this.findTargetContainer();e&&(s.appendChild(e,t),this.showContainer(t))}findTargetContainer(){const t=["#swiperTagContainer",".TagTiles",".IndexPage-nav",".App-content","body"];for(const e of t){const n=s.querySelector(e);if(n)return n}return null}showContainer(t){s.setStyles(t,{opacity:"0",transform:"translateY(-10px)",transition:"all 0.3s ease"}),setTimeout(()=>{s.setStyles(t,{opacity:"1",transform:"translateY(0)",display:"block"})},100)}async refreshButtons(){const t=s.getElementById(this.containerId);t&&s.removeElement(t),await this.dataLoader.refreshButtonsCustomizationList(),await this.addButtonsCustomization()}hideButtons(){const t=s.getElementById(this.containerId);t&&s.setStyles(t,{display:"none"})}showButtons(){const t=s.getElementById(this.containerId);t&&s.setStyles(t,{display:"block"})}areButtonsVisible(){const t=s.getElementById(this.containerId);return t!==null&&t.style.display!=="none"}}m.initializers.add("wusong8899-client1-buttons-customization",()=>{m.store.models.buttonsCustomizationList=b;const a=l.getInstance(),t=new y,e=c.getInstance();if(C.extend(p.prototype,"view",function(n){this.shouldShowButtons()&&setTimeout(()=>{t.initialize().catch(()=>{})},200)}),p.prototype.shouldShowButtons=function(){const n=window.location.pathname;return["/","/tags","/d/","/u/"].some(o=>n===o||n.startsWith(o))},a.getBehaviorSetting("autoRefresh")){const n=a.getBehaviorSetting("refreshInterval");setInterval(()=>{t.areButtonsVisible()&&e.refreshButtonsCustomizationList().catch(()=>{})},n)}window.ButtonsCustomization={uiManager:t,dataLoader:e,configManager:a,refreshButtons:()=>t.refreshButtons(),hideButtons:()=>t.hideButtons(),showButtons:()=>t.showButtons(),getButtonsData:()=>e.getButtonsCustomizationList(),...a.isDebugEnabled()&&{debug:{getConfig:()=>a.getConfig(),getState:()=>({isLoading:e.isLoading(),hasData:e.hasData(),dataCount:e.getDataCount(),areButtonsVisible:t.areButtonsVisible()}),forceReload:()=>e.forceReload(),exportConfig:()=>a.exportConfig()}}}})})(flarum.core.compat["forum/app"],flarum.core.compat["common/extend"],flarum.core.compat["forum/components/HeaderPrimary"],flarum.core.compat["common/Model"]);
//# sourceMappingURL=forum.js.map

module.exports={};