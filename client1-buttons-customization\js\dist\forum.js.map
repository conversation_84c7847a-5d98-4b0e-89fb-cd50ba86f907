{"version": 3, "file": "forum.js", "sources": ["../src/forum/model/ButtonsCustomization.ts", "../src/forum/services/ButtonsCustomizationDataLoader.ts", "../src/forum/utils/DOMUtils.ts", "../src/forum/utils/MobileDetection.ts", "../src/common/config/defaults.ts", "../src/common/config/ConfigManager.ts", "../src/forum/components/ButtonsCustomizationUIManager.ts", "../src/forum/index.ts"], "sourcesContent": ["import Model from \"flarum/common/Model\";\r\n\r\nexport default class ButtonsCustomization extends Model {\r\n  id!: () => string | undefined;\r\n  name!: () => string;\r\n  icon!: () => string;\r\n  color!: () => string;\r\n  url!: () => string;\r\n  sort!: () => number;\r\n}\r\n\r\nObject.assign(ButtonsCustomization.prototype, {\r\n  id: Model.attribute<string>(\"id\"),\r\n  name: Model.attribute<string>(\"name\"),\r\n  icon: Model.attribute<string>(\"icon\"),\r\n  color: Model.attribute<string>(\"color\"),\r\n  url: Model.attribute<string>(\"url\"),\r\n  sort: Model.attribute<number>(\"sort\"),\r\n});\r\n", "import app from 'flarum/forum/app';\n\n/**\n * ButtonsCustomizationDataLoader - Handles loading and caching of buttons customization data\n * Migrated from client1-header-adv extension for independent operation\n */\nexport class ButtonsCustomizationDataLoader {\n    private static instance: ButtonsCustomizationDataLoader;\n\n    // Loading states\n    private buttonsCustomizationListLoading = false;\n\n    // Data storage\n    private buttonsCustomizationList: any[] | null = null;\n\n    private constructor() { }\n\n    /**\n     * Get singleton instance\n     */\n    static getInstance(): ButtonsCustomizationDataLoader {\n        if (!ButtonsCustomizationDataLoader.instance) {\n            ButtonsCustomizationDataLoader.instance = new ButtonsCustomizationDataLoader();\n        }\n        return ButtonsCustomizationDataLoader.instance;\n    }\n\n    /**\n     * Load buttons customization data\n     * @returns {Promise<any[]>} Promise resolving to buttons customization data\n     */\n    async loadButtonsCustomizationList(): Promise<any[]> {\n        if (this.buttonsCustomizationListLoading) {\n            return this.waitForButtonsCustomizationList();\n        }\n\n        if (this.buttonsCustomizationList !== null) {\n            return this.buttonsCustomizationList;\n        }\n\n        this.buttonsCustomizationListLoading = true;\n\n        try {\n            const results = await app.store.find('buttonsCustomizationList').catch(() => []);\n            this.buttonsCustomizationList = [];\n            \n            if (Array.isArray(results)) {\n                this.buttonsCustomizationList.push(...results);\n            } else if (results) {\n                // Handle single item response\n                this.buttonsCustomizationList.push(results);\n            }\n            \n            return this.buttonsCustomizationList;\n        } catch {\n            // Silently handle errors and return empty array\n            this.buttonsCustomizationList = [];\n            return this.buttonsCustomizationList;\n        } finally {\n            this.buttonsCustomizationListLoading = false;\n        }\n    }\n\n    /**\n     * Get cached buttons customization list\n     */\n    getButtonsCustomizationList(): any[] | null {\n        return this.buttonsCustomizationList;\n    }\n\n    /**\n     * Clear cached data\n     */\n    clearCache(): void {\n        this.buttonsCustomizationList = null;\n    }\n\n    /**\n     * Refresh buttons customization data\n     */\n    async refreshButtonsCustomizationList(): Promise<any[]> {\n        this.clearCache();\n        return this.loadButtonsCustomizationList();\n    }\n\n    /**\n     * Check if data is currently loading\n     */\n    isLoading(): boolean {\n        return this.buttonsCustomizationListLoading;\n    }\n\n    /**\n     * Check if data is available\n     */\n    hasData(): boolean {\n        return this.buttonsCustomizationList !== null && this.buttonsCustomizationList.length > 0;\n    }\n\n    /**\n     * Get data count\n     */\n    getDataCount(): number {\n        return this.buttonsCustomizationList ? this.buttonsCustomizationList.length : 0;\n    }\n\n    /**\n     * Force reload data (bypass cache)\n     */\n    async forceReload(): Promise<any[]> {\n        this.buttonsCustomizationListLoading = false; // Reset loading state\n        this.clearCache();\n        return this.loadButtonsCustomizationList();\n    }\n\n    /**\n     * Get button by ID\n     */\n    getButtonById(id: string): any | null {\n        if (!this.buttonsCustomizationList) {\n            return null;\n        }\n        return this.buttonsCustomizationList.find(button => button.id && button.id() === id) || null;\n    }\n\n    /**\n     * Get buttons sorted by sort order\n     */\n    getSortedButtons(): any[] {\n        if (!this.buttonsCustomizationList) {\n            return [];\n        }\n        return [...this.buttonsCustomizationList].sort((a, b) => {\n            const sortA = a.sort ? a.sort() : 0;\n            const sortB = b.sort ? b.sort() : 0;\n            return sortA - sortB;\n        });\n    }\n\n    /**\n     * Helper method for waiting for data to load\n     */\n    private async waitForButtonsCustomizationList(): Promise<any[]> {\n        return new Promise((resolve) => {\n            const checkInterval = setInterval(() => {\n                if (!this.buttonsCustomizationListLoading && this.buttonsCustomizationList !== null) {\n                    clearInterval(checkInterval);\n                    resolve(this.buttonsCustomizationList);\n                }\n            }, 100);\n            \n            // Add timeout to prevent infinite waiting\n            setTimeout(() => {\n                clearInterval(checkInterval);\n                resolve(this.buttonsCustomizationList || []);\n            }, 10000); // 10 second timeout\n        });\n    }\n}\n", "/**\n * DOM manipulation utilities for buttons customization\n * Migrated from client1-header-adv extension for independent operation\n */\nexport class DOMUtils {\n    /**\n     * Create a DOM element with specified attributes\n     * @param {string} tagName - HTML tag name\n     * @param {object} attributes - Element attributes\n     * @param {string} innerHTML - Inner HTML content\n     * @returns {HTMLElement} Created element\n     */\n    static createElement(\n        tagName: string, \n        attributes: Record<string, string> = {}, \n        innerHTML: string = ''\n    ): HTMLElement {\n        const element = document.createElement(tagName);\n        \n        Object.entries(attributes).forEach(([key, value]) => {\n            if (key === 'className') {\n                element.className = value;\n            } else if (key === 'style') {\n                element.setAttribute('style', value);\n            } else {\n                element.setAttribute(key, value);\n            }\n        });\n        \n        if (innerHTML) {\n            element.innerHTML = innerHTML;\n        }\n        \n        return element;\n    }\n\n    /**\n     * Safely get element by ID\n     * @param {string} id - Element ID\n     * @returns {HTMLElement | null} Element or null if not found\n     */\n    static getElementById(id: string): HTMLElement | null {\n        return document.getElementById(id);\n    }\n\n    /**\n     * Safely query selector\n     * @param {string} selector - CSS selector\n     * @param {Element} parent - Parent element (default: document)\n     * @returns {Element | null} Element or null if not found\n     */\n    static querySelector(selector: string, parent: Element | Document = document): Element | null {\n        try {\n            return parent.querySelector(selector);\n        } catch (error) {\n            console.warn(`Invalid selector: ${selector}`, error);\n            return null;\n        }\n    }\n\n    /**\n     * Safely query all elements\n     * @param {string} selector - CSS selector\n     * @param {Element} parent - Parent element (default: document)\n     * @returns {NodeListOf<Element>} NodeList of elements\n     */\n    static querySelectorAll(selector: string, parent: Element | Document = document): NodeListOf<Element> {\n        try {\n            return parent.querySelectorAll(selector);\n        } catch (error) {\n            console.warn(`Invalid selector: ${selector}`, error);\n            return document.querySelectorAll(''); // Return empty NodeList\n        }\n    }\n\n    /**\n     * Add event listener with error handling\n     * @param {Element} element - Target element\n     * @param {string} event - Event type\n     * @param {Function} handler - Event handler\n     * @param {boolean} useCapture - Use capture phase\n     */\n    static addEventListener(\n        element: Element, \n        event: string, \n        handler: EventListener, \n        useCapture: boolean = false\n    ): void {\n        try {\n            element.addEventListener(event, handler, useCapture);\n        } catch (error) {\n            console.error('Failed to add event listener:', error);\n        }\n    }\n\n    /**\n     * Remove event listener with error handling\n     * @param {Element} element - Target element\n     * @param {string} event - Event type\n     * @param {Function} handler - Event handler\n     * @param {boolean} useCapture - Use capture phase\n     */\n    static removeEventListener(\n        element: Element, \n        event: string, \n        handler: EventListener, \n        useCapture: boolean = false\n    ): void {\n        try {\n            element.removeEventListener(event, handler, useCapture);\n        } catch (error) {\n            console.error('Failed to remove event listener:', error);\n        }\n    }\n\n    /**\n     * Set CSS styles on element\n     * @param {HTMLElement} element - Target element\n     * @param {object} styles - Style properties\n     */\n    static setStyles(element: HTMLElement, styles: Record<string, string>): void {\n        Object.entries(styles).forEach(([property, value]) => {\n            try {\n                element.style.setProperty(property, value);\n            } catch (error) {\n                console.warn(`Failed to set style ${property}: ${value}`, error);\n            }\n        });\n    }\n\n    /**\n     * Append element to parent with error handling\n     * @param {Element} parent - Parent element\n     * @param {Element} child - Child element to append\n     */\n    static appendChild(parent: Element, child: Element): void {\n        try {\n            parent.appendChild(child);\n        } catch (error) {\n            console.error('Failed to append child element:', error);\n        }\n    }\n\n    /**\n     * Prepend element to parent with error handling\n     * @param {Element} parent - Parent element\n     * @param {Element} child - Child element to prepend\n     */\n    static prependChild(parent: Element, child: Element): void {\n        try {\n            parent.insertBefore(child, parent.firstChild);\n        } catch (error) {\n            console.error('Failed to prepend child element:', error);\n        }\n    }\n\n    /**\n     * Remove element safely\n     * @param {Element} element - Element to remove\n     */\n    static removeElement(element: Element): void {\n        try {\n            if (element && element.parentNode) {\n                element.parentNode.removeChild(element);\n            }\n        } catch (error) {\n            console.error('Failed to remove element:', error);\n        }\n    }\n\n    /**\n     * Check if element is visible\n     * @param {Element} element - Element to check\n     * @returns {boolean} True if element is visible\n     */\n    static isVisible(element: Element): boolean {\n        try {\n            const htmlElement = element as HTMLElement;\n            return !!(htmlElement.offsetWidth || htmlElement.offsetHeight || htmlElement.getClientRects().length);\n        } catch {\n            return false;\n        }\n    }\n\n    /**\n     * Get element dimensions\n     * @param {Element} element - Element to measure\n     * @returns {object} Width and height\n     */\n    static getDimensions(element: Element): { width: number, height: number } {\n        try {\n            const htmlElement = element as HTMLElement;\n            return {\n                width: htmlElement.offsetWidth,\n                height: htmlElement.offsetHeight\n            };\n        } catch {\n            return { width: 0, height: 0 };\n        }\n    }\n\n    /**\n     * Wait for element to appear in DOM\n     * @param {string} selector - CSS selector\n     * @param {number} timeout - Timeout in milliseconds\n     * @returns {Promise<Element>} Promise resolving to element\n     */\n    static waitForElement(selector: string, timeout: number = 5000): Promise<Element> {\n        return new Promise((resolve, reject) => {\n            const element = this.querySelector(selector);\n            if (element) {\n                resolve(element);\n                return;\n            }\n\n            const observer = new MutationObserver(() => {\n                const element = this.querySelector(selector);\n                if (element) {\n                    observer.disconnect();\n                    resolve(element);\n                }\n            });\n\n            observer.observe(document.body, {\n                childList: true,\n                subtree: true\n            });\n\n            setTimeout(() => {\n                observer.disconnect();\n                reject(new Error(`Element ${selector} not found within ${timeout}ms`));\n            }, timeout);\n        });\n    }\n}\n", "/**\n * Mobile device detection utility for buttons customization\n * Migrated from client1-header-adv extension for independent operation\n */\nexport class MobileDetection {\n    private static isMobile: boolean | null = null;\n\n    /**\n     * Check if the current device is mobile\n     * @returns {boolean} True if mobile device\n     */\n    static isMobileDevice(): boolean {\n        if (this.isMobile !== null) {\n            return this.isMobile;\n        }\n\n        let check = false;\n        const userAgent = navigator.userAgent || navigator.vendor || (window as any).opera;\n        \n        // Mobile detection regex\n        const mobileRegex = /(android|bb\\d+|meego).+mobile|avantgo|bada\\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i;\n        \n        const mobileRegex2 = /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw-(n|u)|c55\\/|capi|ccwa|cdm-|cell|chtm|cldc|cmd-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc-s|devi|dica|dmob|do(c|p)o|ds(12|-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(-|_)|g1 u|g560|gene|gf-5|g-mo|go(\\.w|od)|gr(ad|un)|haie|hcit|hd-(m|p|t)|hei-|hi(pt|ta)|hp( i|ip)|hs-c|ht(c(-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i-(20|go|ma)|i230|iac( |-|\\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\\/)|klon|kpt |kwc-|kyo(c|k)|le(no|xi)|lg( g|\\/(k|l|u)|50|54|-[a-w])|libw|lynx|m1-w|m3ga|m50\\/|ma(te|ui|xo)|mc(01|21|ca)|m-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|-([1-8]|c))|phil|pire|pl(ay|uc)|pn-2|po(ck|rt|se)|prox|psio|pt-g|qa-a|qc(07|12|21|32|60|-[2-7]|i-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h-|oo|p-)|sdk\\/|se(c(-|0|1)|47|mc|nd|ri)|sgh-|shar|sie(-|m)|sk-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h-|v-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl-|tdg-|tel(i|m)|tim-|t-mo|to(pl|sh)|ts(70|m-|m3|m5)|tx-9|up(\\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas-|your|zeto|zte-/i;\n\n        if (mobileRegex.test(userAgent) || mobileRegex2.test(userAgent.substr(0, 4))) {\n            check = true;\n        }\n\n        this.isMobile = check;\n        return check;\n    }\n\n    /**\n     * Get event type based on device\n     * @returns {string} 'touchend' for mobile, 'click' for desktop\n     */\n    static getEventType(): string {\n        return this.isMobileDevice() ? 'touchend' : 'click';\n    }\n\n    /**\n     * Get responsive button configuration\n     * @returns {object} Configuration object with mobile-specific settings\n     */\n    static getButtonConfig() {\n        const isMobile = this.isMobileDevice();\n        return {\n            buttonSize: isMobile ? 'small' : 'medium',\n            spacing: isMobile ? 8 : 12,\n            fontSize: isMobile ? '14px' : '16px',\n            padding: isMobile ? '8px 12px' : '10px 16px',\n            maxButtonsPerRow: isMobile ? 2 : 4\n        };\n    }\n\n    /**\n     * Get responsive layout configuration\n     * @returns {object} Layout configuration for different screen sizes\n     */\n    static getLayoutConfig() {\n        const isMobile = this.isMobileDevice();\n        return {\n            containerWidth: isMobile ? '100%' : 'auto',\n            containerPadding: isMobile ? '10px' : '15px',\n            buttonMargin: isMobile ? '5px' : '8px',\n            flexDirection: isMobile ? 'column' : 'row',\n            alignItems: isMobile ? 'stretch' : 'center'\n        };\n    }\n\n    /**\n     * Check if device supports touch\n     * @returns {boolean} True if touch is supported\n     */\n    static isTouchDevice(): boolean {\n        return 'ontouchstart' in window || navigator.maxTouchPoints > 0;\n    }\n\n    /**\n     * Get screen size category\n     * @returns {string} Screen size category\n     */\n    static getScreenSize(): string {\n        const width = window.innerWidth;\n        \n        if (width < 576) {\n            return 'xs'; // Extra small\n        } else if (width < 768) {\n            return 'sm'; // Small\n        } else if (width < 992) {\n            return 'md'; // Medium\n        } else if (width < 1200) {\n            return 'lg'; // Large\n        } else {\n            return 'xl'; // Extra large\n        }\n    }\n\n    /**\n     * Get responsive breakpoint configuration\n     * @returns {object} Breakpoint configuration\n     */\n    static getBreakpoints() {\n        return {\n            xs: { max: 575, buttonsPerRow: 1, fontSize: '12px' },\n            sm: { min: 576, max: 767, buttonsPerRow: 2, fontSize: '13px' },\n            md: { min: 768, max: 991, buttonsPerRow: 3, fontSize: '14px' },\n            lg: { min: 992, max: 1199, buttonsPerRow: 4, fontSize: '15px' },\n            xl: { min: 1200, buttonsPerRow: 5, fontSize: '16px' }\n        };\n    }\n\n    /**\n     * Get current breakpoint configuration\n     * @returns {object} Current breakpoint settings\n     */\n    static getCurrentBreakpoint() {\n        const screenSize = this.getScreenSize();\n        const breakpoints = this.getBreakpoints();\n        return breakpoints[screenSize as keyof typeof breakpoints];\n    }\n}\n", "/**\n * Default configuration for client1-buttons-customization extension\n * Independent configuration for buttons customization functionality\n */\n\nexport default {\n  app: {\n    extensionId: 'wusong8899-client1-buttons-customization',\n    name: 'Client1 Buttons Customization',\n    version: '1.0.0',\n  },\n  api: {\n    endpoints: {\n      buttonsList: '/api/buttonsCustomizationList',\n      buttonAdd: '/api/buttonsCustomizationList',\n      buttonUpdate: '/api/buttonsCustomizationList/{id}',\n      buttonDelete: '/api/buttonsCustomizationList/{id}',\n      buttonSort: '/api/buttonsCustomizationList/order',\n    },\n  },\n  data: {\n    apiResources: {\n      buttonsCustomizationList: 'buttonsCustomizationList',\n    },\n  },\n  ui: {\n    containerIds: {\n      buttonsContainer: 'buttonCustomizationContainer',\n      buttonsWrapper: 'buttons-customization-wrapper',\n    },\n    classes: {\n      container: 'buttonCustomizationContainer',\n      wrapper: 'buttons-customization-wrapper',\n      header: 'buttons-customization-header',\n      buttonItem: 'custom-button-item',\n      buttonLink: 'custom-button-link',\n      buttonContent: 'custom-button-content',\n      buttonText: 'custom-button-text',\n    },\n    animation: {\n      duration: 300,\n      easing: 'ease',\n      fadeInDelay: 100,\n    },\n  },\n  layout: {\n    responsive: {\n      xs: { buttonsPerRow: 1, fontSize: '12px', padding: '6px 10px' },\n      sm: { buttonsPerRow: 2, fontSize: '13px', padding: '7px 12px' },\n      md: { buttonsPerRow: 3, fontSize: '14px', padding: '8px 14px' },\n      lg: { buttonsPerRow: 4, fontSize: '15px', padding: '9px 16px' },\n      xl: { buttonsPerRow: 5, fontSize: '16px', padding: '10px 18px' },\n    },\n    spacing: {\n      containerPadding: '15px',\n      buttonMargin: '8px',\n      iconMargin: '8px',\n    },\n  },\n  behavior: {\n    autoRefresh: true,\n    refreshInterval: 300000, // 5 minutes\n    loadTimeout: 10000, // 10 seconds\n    retryAttempts: 3,\n    retryDelay: 1000, // 1 second\n  },\n  styling: {\n    defaultColors: {\n      primary: '#007bff',\n      secondary: '#6c757d',\n      success: '#28a745',\n      danger: '#dc3545',\n      warning: '#ffc107',\n      info: '#17a2b8',\n      light: '#f8f9fa',\n      dark: '#343a40',\n    },\n    buttonStyles: {\n      borderRadius: '6px',\n      transition: 'all 0.2s ease',\n      fontWeight: '500',\n      textDecoration: 'none',\n      display: 'inline-flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n    },\n  },\n  accessibility: {\n    enableKeyboardNavigation: true,\n    enableScreenReader: true,\n    focusOutlineColor: '#007bff',\n    highContrastMode: false,\n  },\n  performance: {\n    enableVirtualization: false,\n    lazyLoading: true,\n    debounceDelay: 250,\n    cacheTimeout: 600000, // 10 minutes\n  },\n  debug: {\n    enableLogging: false,\n    logLevel: 'warn', // 'debug', 'info', 'warn', 'error'\n    enablePerformanceMonitoring: false,\n  },\n};\n", "import defaultConfig from './defaults';\nimport { ButtonsCustomizationConfig } from './types';\n\n/**\n * Configuration manager for buttons customization extension\n * Provides centralized configuration management with validation and overrides\n */\nexport class ConfigManager {\n    private static instance: ConfigManager;\n    private config: ButtonsCustomizationConfig;\n    private overrides: Partial<ButtonsCustomizationConfig> = {};\n\n    private constructor() {\n        this.config = { ...defaultConfig };\n    }\n\n    /**\n     * Get singleton instance\n     */\n    static getInstance(): ConfigManager {\n        if (!ConfigManager.instance) {\n            ConfigManager.instance = new ConfigManager();\n        }\n        return ConfigManager.instance;\n    }\n\n    /**\n     * Get complete configuration\n     */\n    getConfig(): ButtonsCustomizationConfig {\n        return this.mergeConfig(this.config, this.overrides);\n    }\n\n    /**\n     * Get specific configuration section\n     */\n    getSection<K extends keyof ButtonsCustomizationConfig>(section: K): ButtonsCustomizationConfig[K] {\n        const config = this.getConfig();\n        return config[section];\n    }\n\n    /**\n     * Set configuration overrides\n     */\n    setOverrides(overrides: Partial<ButtonsCustomizationConfig>): void {\n        this.overrides = { ...this.overrides, ...overrides };\n    }\n\n    /**\n     * Get API resource name\n     */\n    getApiResource(): string {\n        return this.getSection('data').apiResources.buttonsCustomizationList;\n    }\n\n    /**\n     * Get API endpoint\n     */\n    getApiEndpoint(endpoint: keyof ButtonsCustomizationConfig['api']['endpoints']): string {\n        return this.getSection('api').endpoints[endpoint];\n    }\n\n    /**\n     * Get UI class name\n     */\n    getUIClass(className: keyof ButtonsCustomizationConfig['ui']['classes']): string {\n        return this.getSection('ui').classes[className];\n    }\n\n    /**\n     * Get container ID\n     */\n    getContainerId(containerId: keyof ButtonsCustomizationConfig['ui']['containerIds']): string {\n        return this.getSection('ui').containerIds[containerId];\n    }\n\n    /**\n     * Get responsive configuration for current screen size\n     */\n    getResponsiveConfig(): ButtonsCustomizationConfig['layout']['responsive'][keyof ButtonsCustomizationConfig['layout']['responsive']] {\n        const screenSize = this.getCurrentScreenSize();\n        return this.getSection('layout').responsive[screenSize];\n    }\n\n    /**\n     * Get current screen size category\n     */\n    private getCurrentScreenSize(): keyof ButtonsCustomizationConfig['layout']['responsive'] {\n        const width = window.innerWidth;\n        \n        if (width < 576) {\n            return 'xs';\n        } else if (width < 768) {\n            return 'sm';\n        } else if (width < 992) {\n            return 'md';\n        } else if (width < 1200) {\n            return 'lg';\n        } else {\n            return 'xl';\n        }\n    }\n\n    /**\n     * Get behavior setting\n     */\n    getBehaviorSetting<K extends keyof ButtonsCustomizationConfig['behavior']>(\n        setting: K\n    ): ButtonsCustomizationConfig['behavior'][K] {\n        return this.getSection('behavior')[setting];\n    }\n\n    /**\n     * Get styling setting\n     */\n    getStyleSetting<K extends keyof ButtonsCustomizationConfig['styling']>(\n        setting: K\n    ): ButtonsCustomizationConfig['styling'][K] {\n        return this.getSection('styling')[setting];\n    }\n\n    /**\n     * Check if debug mode is enabled\n     */\n    isDebugEnabled(): boolean {\n        return this.getSection('debug').enableLogging;\n    }\n\n    /**\n     * Get debug log level\n     */\n    getLogLevel(): ButtonsCustomizationConfig['debug']['logLevel'] {\n        return this.getSection('debug').logLevel;\n    }\n\n    /**\n     * Validate configuration\n     */\n    validateConfig(): { isValid: boolean; errors: string[] } {\n        const errors: string[] = [];\n        const config = this.getConfig();\n\n        // Validate required fields\n        if (!config.app.extensionId) {\n            errors.push('Extension ID is required');\n        }\n\n        if (!config.data.apiResources.buttonsCustomizationList) {\n            errors.push('API resource name is required');\n        }\n\n        // Validate numeric values\n        if (config.behavior.refreshInterval < 1000) {\n            errors.push('Refresh interval must be at least 1000ms');\n        }\n\n        if (config.behavior.loadTimeout < 1000) {\n            errors.push('Load timeout must be at least 1000ms');\n        }\n\n        if (config.behavior.retryAttempts < 0) {\n            errors.push('Retry attempts must be non-negative');\n        }\n\n        return {\n            isValid: errors.length === 0,\n            errors\n        };\n    }\n\n    /**\n     * Reset configuration to defaults\n     */\n    resetToDefaults(): void {\n        this.config = { ...defaultConfig };\n        this.overrides = {};\n    }\n\n    /**\n     * Merge configuration objects deeply\n     */\n    private mergeConfig(\n        base: ButtonsCustomizationConfig, \n        overrides: Partial<ButtonsCustomizationConfig>\n    ): ButtonsCustomizationConfig {\n        const result = { ...base };\n\n        for (const key in overrides) {\n            if (overrides.hasOwnProperty(key)) {\n                const value = overrides[key as keyof ButtonsCustomizationConfig];\n                if (value && typeof value === 'object' && !Array.isArray(value)) {\n                    result[key as keyof ButtonsCustomizationConfig] = {\n                        ...result[key as keyof ButtonsCustomizationConfig],\n                        ...value\n                    } as any;\n                } else {\n                    result[key as keyof ButtonsCustomizationConfig] = value as any;\n                }\n            }\n        }\n\n        return result;\n    }\n\n    /**\n     * Export configuration as JSON\n     */\n    exportConfig(): string {\n        return JSON.stringify(this.getConfig(), null, 2);\n    }\n\n    /**\n     * Import configuration from JSON\n     */\n    importConfig(configJson: string): { success: boolean; error?: string } {\n        try {\n            const importedConfig = JSON.parse(configJson);\n            this.setOverrides(importedConfig);\n            \n            const validation = this.validateConfig();\n            if (!validation.isValid) {\n                return {\n                    success: false,\n                    error: `Configuration validation failed: ${validation.errors.join(', ')}`\n                };\n            }\n\n            return { success: true };\n        } catch (error) {\n            return {\n                success: false,\n                error: `Failed to parse configuration: ${error}`\n            };\n        }\n    }\n}\n", "import app from 'flarum/forum/app';\nimport { ButtonsCustomizationDataLoader } from '../services/ButtonsCustomizationDataLoader';\nimport { DOMUtils } from '../utils/DOMUtils';\nimport { MobileDetection } from '../utils/MobileDetection';\nimport { ConfigManager } from '../../common/config/ConfigManager';\n\n/**\n * ButtonsCustomizationUIManager - Manages buttons customization UI components and interactions\n * Migrated from client1-header-adv extension for independent operation\n */\nexport class ButtonsCustomizationUIManager {\n    private dataLoader: ButtonsCustomizationDataLoader;\n    private configManager: ConfigManager;\n    private containerId: string;\n\n    constructor() {\n        this.dataLoader = ButtonsCustomizationDataLoader.getInstance();\n        this.configManager = ConfigManager.getInstance();\n        this.containerId = this.configManager.getContainerId('buttonsContainer');\n    }\n\n    /**\n     * Initialize buttons customization UI components\n     */\n    async initialize(): Promise<void> {\n        await this.addButtonsCustomization();\n    }\n\n    /**\n     * Add buttons customization component\n     */\n    private async addButtonsCustomization(): Promise<void> {\n        const buttonsList = await this.dataLoader.loadButtonsCustomizationList();\n\n        if (DOMUtils.getElementById(this.containerId) || buttonsList.length === 0) {\n            return;\n        }\n\n        const container = this.createButtonsContainer();\n        const buttonsWrapper = this.createButtonsWrapper(container);\n\n        this.populateButtons(buttonsWrapper, buttonsList);\n        this.attachToDOM(container);\n    }\n\n    /**\n     * Create buttons container\n     */\n    private createButtonsContainer(): HTMLElement {\n        const container = DOMUtils.createElement('div', {\n            id: this.containerId,\n            className: 'buttonCustomizationContainer'\n        });\n\n        // Add container title or header if needed\n        const header = DOMUtils.createElement('div', {\n            className: 'buttons-customization-header'\n        }, '自定义按钮');\n\n        DOMUtils.appendChild(container, header);\n        return container;\n    }\n\n    /**\n     * Create buttons wrapper\n     */\n    private createButtonsWrapper(container: HTMLElement): HTMLElement {\n        const wrapper = DOMUtils.createElement('div', {\n            className: 'buttons-customization-wrapper'\n        });\n\n        DOMUtils.appendChild(container, wrapper);\n        return wrapper;\n    }\n\n    /**\n     * Populate buttons in the wrapper\n     */\n    private populateButtons(wrapper: HTMLElement, _buttonsList: any[]): void {\n        // Sort buttons by sort order\n        const sortedButtons = this.dataLoader.getSortedButtons();\n\n        sortedButtons.forEach(buttonData => {\n            const button = this.createButton(buttonData);\n            DOMUtils.appendChild(wrapper, button);\n        });\n    }\n\n    /**\n     * Create individual button element\n     */\n    private createButton(buttonData: any): HTMLElement {\n        const name = buttonData.name ? buttonData.name() : 'Button';\n        const url = buttonData.url ? buttonData.url() : '#';\n        const icon = buttonData.icon ? buttonData.icon() : '';\n        const color = buttonData.color ? buttonData.color() : '';\n\n        const button = DOMUtils.createElement('div', {\n            className: 'custom-button-item'\n        });\n\n        // Create button content\n        const buttonContent = this.createButtonContent(name, url, icon, color);\n        button.innerHTML = buttonContent;\n\n        // Add click event\n        this.addButtonClickEvent(button, url);\n\n        return button;\n    }\n\n    /**\n     * Create button content HTML\n     */\n    private createButtonContent(name: string, url: string, icon: string, color: string): string {\n        const iconHtml = icon ? `<i class=\"${icon}\" style=\"margin-right: 8px;\"></i>` : '';\n        const colorStyle = color ? `color: ${color};` : '';\n\n        return `\n            <a href=\"${url}\" target=\"_blank\" class=\"custom-button-link\" style=\"${colorStyle}\">\n                <div class=\"custom-button-content\">\n                    ${iconHtml}\n                    <span class=\"custom-button-text\">${name}</span>\n                </div>\n            </a>\n        `;\n    }\n\n    /**\n     * Add click event to button\n     */\n    private addButtonClickEvent(button: HTMLElement, url: string): void {\n        DOMUtils.addEventListener(button, MobileDetection.getEventType(), (event) => {\n            event.preventDefault();\n            if (url && url !== '#') {\n                window.open(url, '_blank');\n            }\n        });\n    }\n\n    /**\n     * Attach container to DOM\n     */\n    private attachToDOM(container: HTMLElement): void {\n        // Try to find a suitable parent container\n        const targetContainer = this.findTargetContainer();\n\n        if (targetContainer) {\n            DOMUtils.appendChild(targetContainer, container);\n            this.showContainer(container);\n        }\n    }\n\n    /**\n     * Find target container for buttons\n     */\n    private findTargetContainer(): HTMLElement | null {\n        // Try multiple possible containers\n        const possibleContainers = [\n            '#swiperTagContainer',\n            '.TagTiles',\n            '.IndexPage-nav',\n            '.App-content',\n            'body'\n        ];\n\n        for (const selector of possibleContainers) {\n            const container = DOMUtils.querySelector(selector);\n            if (container) {\n                return container as HTMLElement;\n            }\n        }\n\n        return null;\n    }\n\n    /**\n     * Show container with animation\n     */\n    private showContainer(container: HTMLElement): void {\n        // Initially hide the container\n        DOMUtils.setStyles(container, {\n            'opacity': '0',\n            'transform': 'translateY(-10px)',\n            'transition': 'all 0.3s ease'\n        });\n\n        // Show with animation\n        setTimeout(() => {\n            DOMUtils.setStyles(container, {\n                'opacity': '1',\n                'transform': 'translateY(0)',\n                'display': 'block'\n            });\n        }, 100);\n    }\n\n    /**\n     * Refresh buttons display\n     */\n    async refreshButtons(): Promise<void> {\n        const existingContainer = DOMUtils.getElementById(this.containerId);\n        if (existingContainer) {\n            DOMUtils.removeElement(existingContainer);\n        }\n\n        await this.dataLoader.refreshButtonsCustomizationList();\n        await this.addButtonsCustomization();\n    }\n\n    /**\n     * Hide buttons container\n     */\n    hideButtons(): void {\n        const container = DOMUtils.getElementById(this.containerId);\n        if (container) {\n            DOMUtils.setStyles(container, { 'display': 'none' });\n        }\n    }\n\n    /**\n     * Show buttons container\n     */\n    showButtons(): void {\n        const container = DOMUtils.getElementById(this.containerId);\n        if (container) {\n            DOMUtils.setStyles(container, { 'display': 'block' });\n        }\n    }\n\n    /**\n     * Check if buttons are currently visible\n     */\n    areButtonsVisible(): boolean {\n        const container = DOMUtils.getElementById(this.containerId);\n        return container !== null && container.style.display !== 'none';\n    }\n}\n", "import app from 'flarum/forum/app';\r\nimport { extend } from 'flarum/common/extend';\r\nimport HeaderPrimary from 'flarum/forum/components/HeaderPrimary';\r\nimport ButtonsCustomization from './model/ButtonsCustomization';\r\nimport { ButtonsCustomizationUIManager } from './components/ButtonsCustomizationUIManager';\r\nimport { ButtonsCustomizationDataLoader } from './services/ButtonsCustomizationDataLoader';\r\nimport { ConfigManager } from '../common/config/ConfigManager';\r\n\r\n/**\r\n * Initialize the ButtonsCustomization extension for the forum frontend\r\n */\r\napp.initializers.add('wusong8899-client1-buttons-customization', () => {\r\n  // Register the ButtonsCustomization model with the store\r\n  app.store.models.buttonsCustomizationList = ButtonsCustomization;\r\n\r\n  // Initialize configuration manager\r\n  const configManager = ConfigManager.getInstance();\r\n\r\n  // Initialize UI manager and data loader\r\n  const uiManager = new ButtonsCustomizationUIManager();\r\n  const dataLoader = ButtonsCustomizationDataLoader.getInstance();\r\n\r\n  // Extend HeaderPrimary to add buttons customization UI\r\n  extend(HeaderPrimary.prototype, 'view', function (_vnode) {\r\n    // Check if we're on an appropriate page for buttons display\r\n    if (this.shouldShowButtons()) {\r\n      // Initialize buttons UI components with delay to ensure DOM is ready\r\n      setTimeout(() => {\r\n        uiManager.initialize().catch(() => {\r\n          // Silently handle initialization errors\r\n        });\r\n      }, 200);\r\n    }\r\n  });\r\n\r\n  // Add helper method to check if buttons should be shown\r\n  HeaderPrimary.prototype.shouldShowButtons = function () {\r\n    const currentPath = window.location.pathname;\r\n\r\n    // Show buttons on main pages (customize as needed)\r\n    const allowedPaths = ['/', '/tags', '/d/', '/u/'];\r\n\r\n    return allowedPaths.some(path =>\r\n      currentPath === path || currentPath.startsWith(path)\r\n    );\r\n  };\r\n\r\n  // Auto-refresh buttons periodically if enabled\r\n  if (configManager.getBehaviorSetting('autoRefresh')) {\r\n    const refreshInterval = configManager.getBehaviorSetting('refreshInterval');\r\n\r\n    setInterval(() => {\r\n      if (uiManager.areButtonsVisible()) {\r\n        dataLoader.refreshButtonsCustomizationList().catch(() => {\r\n          // Silently handle refresh errors\r\n        });\r\n      }\r\n    }, refreshInterval);\r\n  }\r\n\r\n  // Add global methods for external access (optional)\r\n  (window as any).ButtonsCustomization = {\r\n    uiManager,\r\n    dataLoader,\r\n    configManager,\r\n\r\n    // Public API methods\r\n    refreshButtons: () => uiManager.refreshButtons(),\r\n    hideButtons: () => uiManager.hideButtons(),\r\n    showButtons: () => uiManager.showButtons(),\r\n    getButtonsData: () => dataLoader.getButtonsCustomizationList(),\r\n\r\n    // Debug methods (only in debug mode)\r\n    ...(configManager.isDebugEnabled() && {\r\n      debug: {\r\n        getConfig: () => configManager.getConfig(),\r\n        getState: () => ({\r\n          isLoading: dataLoader.isLoading(),\r\n          hasData: dataLoader.hasData(),\r\n          dataCount: dataLoader.getDataCount(),\r\n          areButtonsVisible: uiManager.areButtonsVisible()\r\n        }),\r\n        forceReload: () => dataLoader.forceReload(),\r\n        exportConfig: () => configManager.exportConfig()\r\n      }\r\n    })\r\n  };\r\n\r\n  // Initialize extension (debug logging removed for production)\r\n});"], "names": ["ButtonsCustomization", "Model", "ButtonsCustomizationDataLoader", "results", "app", "id", "button", "a", "b", "sortA", "sortB", "resolve", "checkInterval", "DOMUtils", "tagName", "attributes", "innerHTML", "element", "key", "value", "selector", "parent", "error", "event", "handler", "useCapture", "styles", "property", "child", "htmlElement", "timeout", "reject", "observer", "_MobileDetection", "check", "userAgent", "mobileRegex", "mobileRegex2", "isMobile", "width", "screenSize", "MobileDetection", "defaultConfig", "ConfigManager", "section", "overrides", "endpoint", "className", "containerId", "setting", "errors", "config", "base", "result", "config<PERSON><PERSON>", "importedConfig", "validation", "ButtonsCustomizationUIManager", "buttonsList", "container", "buttonsWrapper", "header", "wrapper", "_buttonsList", "buttonData", "name", "url", "icon", "color", "buttonContent", "iconHtml", "colorStyle", "targetContainer", "possibleContainers", "existingContainer", "config<PERSON><PERSON><PERSON>", "uiManager", "dataLoader", "extend", "HeaderPrimary", "_vnode", "currentPath", "path", "refreshInterval"], "mappings": "gCAEA,MAAqBA,UAA6BC,CAAM,CAOxD,CAEA,OAAO,OAAOD,EAAqB,UAAW,CAC5C,GAAIC,EAAM,UAAkB,IAAI,EAChC,KAAMA,EAAM,UAAkB,MAAM,EACpC,KAAMA,EAAM,UAAkB,MAAM,EACpC,MAAOA,EAAM,UAAkB,OAAO,EACtC,IAAKA,EAAM,UAAkB,KAAK,EAClC,KAAMA,EAAM,UAAkB,MAAM,CACtC,CAAC,ECZM,MAAMC,CAA+B,CAShC,aAAc,CALtB,KAAQ,gCAAkC,GAG1C,KAAQ,yBAAyC,IAEzB,CAKxB,OAAO,aAA8C,CACjD,OAAKA,EAA+B,WAChCA,EAA+B,SAAW,IAAIA,GAE3CA,EAA+B,QAC1C,CAMA,MAAM,8BAA+C,CACjD,GAAI,KAAK,gCACL,OAAO,KAAK,gCAAA,EAGhB,GAAI,KAAK,2BAA6B,KAClC,OAAO,KAAK,yBAGhB,KAAK,gCAAkC,GAEvC,GAAI,CACA,MAAMC,EAAU,MAAMC,EAAI,MAAM,KAAK,0BAA0B,EAAE,MAAM,IAAM,EAAE,EAC/E,YAAK,yBAA2B,CAAA,EAE5B,MAAM,QAAQD,CAAO,EACrB,KAAK,yBAAyB,KAAK,GAAGA,CAAO,EACtCA,GAEP,KAAK,yBAAyB,KAAKA,CAAO,EAGvC,KAAK,wBAChB,MAAQ,CAEJ,YAAK,yBAA2B,CAAA,EACzB,KAAK,wBAChB,QAAA,CACI,KAAK,gCAAkC,EAC3C,CACJ,CAKA,6BAA4C,CACxC,OAAO,KAAK,wBAChB,CAKA,YAAmB,CACf,KAAK,yBAA2B,IACpC,CAKA,MAAM,iCAAkD,CACpD,YAAK,WAAA,EACE,KAAK,6BAAA,CAChB,CAKA,WAAqB,CACjB,OAAO,KAAK,+BAChB,CAKA,SAAmB,CACf,OAAO,KAAK,2BAA6B,MAAQ,KAAK,yBAAyB,OAAS,CAC5F,CAKA,cAAuB,CACnB,OAAO,KAAK,yBAA2B,KAAK,yBAAyB,OAAS,CAClF,CAKA,MAAM,aAA8B,CAChC,YAAK,gCAAkC,GACvC,KAAK,WAAA,EACE,KAAK,6BAAA,CAChB,CAKA,cAAcE,EAAwB,CAClC,OAAK,KAAK,0BAGH,KAAK,yBAAyB,KAAKC,GAAUA,EAAO,IAAMA,EAAO,OAASD,CAAE,GAAK,IAC5F,CAKA,kBAA0B,CACtB,OAAK,KAAK,yBAGH,CAAC,GAAG,KAAK,wBAAwB,EAAE,KAAK,CAACE,EAAGC,IAAM,CACrD,MAAMC,EAAQF,EAAE,KAAOA,EAAE,OAAS,EAC5BG,EAAQF,EAAE,KAAOA,EAAE,OAAS,EAClC,OAAOC,EAAQC,CACnB,CAAC,EANU,CAAA,CAOf,CAKA,MAAc,iCAAkD,CAC5D,OAAO,IAAI,QAASC,GAAY,CAC5B,MAAMC,EAAgB,YAAY,IAAM,CAChC,CAAC,KAAK,iCAAmC,KAAK,2BAA6B,OAC3E,cAAcA,CAAa,EAC3BD,EAAQ,KAAK,wBAAwB,EAE7C,EAAG,GAAG,EAGN,WAAW,IAAM,CACb,cAAcC,CAAa,EAC3BD,EAAQ,KAAK,0BAA4B,EAAE,CAC/C,EAAG,GAAK,CACZ,CAAC,CACL,CACJ,CC1JO,MAAME,CAAS,CAQlB,OAAO,cACHC,EACAC,EAAqC,CAAA,EACrCC,EAAoB,GACT,CACX,MAAMC,EAAU,SAAS,cAAcH,CAAO,EAE9C,cAAO,QAAQC,CAAU,EAAE,QAAQ,CAAC,CAACG,EAAKC,CAAK,IAAM,CAC7CD,IAAQ,YACRD,EAAQ,UAAYE,EACbD,IAAQ,QACfD,EAAQ,aAAa,QAASE,CAAK,EAEnCF,EAAQ,aAAaC,EAAKC,CAAK,CAEvC,CAAC,EAEGH,IACAC,EAAQ,UAAYD,GAGjBC,CACX,CAOA,OAAO,eAAeZ,EAAgC,CAClD,OAAO,SAAS,eAAeA,CAAE,CACrC,CAQA,OAAO,cAAce,EAAkBC,EAA6B,SAA0B,CAC1F,GAAI,CACA,OAAOA,EAAO,cAAcD,CAAQ,CACxC,OAASE,EAAO,CACZ,eAAQ,KAAK,qBAAqBF,CAAQ,GAAIE,CAAK,EAC5C,IACX,CACJ,CAQA,OAAO,iBAAiBF,EAAkBC,EAA6B,SAA+B,CAClG,GAAI,CACA,OAAOA,EAAO,iBAAiBD,CAAQ,CAC3C,OAASE,EAAO,CACZ,eAAQ,KAAK,qBAAqBF,CAAQ,GAAIE,CAAK,EAC5C,SAAS,iBAAiB,EAAE,CACvC,CACJ,CASA,OAAO,iBACHL,EACAM,EACAC,EACAC,EAAsB,GAClB,CACJ,GAAI,CACAR,EAAQ,iBAAiBM,EAAOC,EAASC,CAAU,CACvD,OAASH,EAAO,CACZ,QAAQ,MAAM,gCAAiCA,CAAK,CACxD,CACJ,CASA,OAAO,oBACHL,EACAM,EACAC,EACAC,EAAsB,GAClB,CACJ,GAAI,CACAR,EAAQ,oBAAoBM,EAAOC,EAASC,CAAU,CAC1D,OAASH,EAAO,CACZ,QAAQ,MAAM,mCAAoCA,CAAK,CAC3D,CACJ,CAOA,OAAO,UAAUL,EAAsBS,EAAsC,CACzE,OAAO,QAAQA,CAAM,EAAE,QAAQ,CAAC,CAACC,EAAUR,CAAK,IAAM,CAClD,GAAI,CACAF,EAAQ,MAAM,YAAYU,EAAUR,CAAK,CAC7C,OAASG,EAAO,CACZ,QAAQ,KAAK,uBAAuBK,CAAQ,KAAKR,CAAK,GAAIG,CAAK,CACnE,CACJ,CAAC,CACL,CAOA,OAAO,YAAYD,EAAiBO,EAAsB,CACtD,GAAI,CACAP,EAAO,YAAYO,CAAK,CAC5B,OAASN,EAAO,CACZ,QAAQ,MAAM,kCAAmCA,CAAK,CAC1D,CACJ,CAOA,OAAO,aAAaD,EAAiBO,EAAsB,CACvD,GAAI,CACAP,EAAO,aAAaO,EAAOP,EAAO,UAAU,CAChD,OAASC,EAAO,CACZ,QAAQ,MAAM,mCAAoCA,CAAK,CAC3D,CACJ,CAMA,OAAO,cAAcL,EAAwB,CACzC,GAAI,CACIA,GAAWA,EAAQ,YACnBA,EAAQ,WAAW,YAAYA,CAAO,CAE9C,OAASK,EAAO,CACZ,QAAQ,MAAM,4BAA6BA,CAAK,CACpD,CACJ,CAOA,OAAO,UAAUL,EAA2B,CACxC,GAAI,CACA,MAAMY,EAAcZ,EACpB,MAAO,CAAC,EAAEY,EAAY,aAAeA,EAAY,cAAgBA,EAAY,iBAAiB,OAClG,MAAQ,CACJ,MAAO,EACX,CACJ,CAOA,OAAO,cAAcZ,EAAqD,CACtE,GAAI,CACA,MAAMY,EAAcZ,EACpB,MAAO,CACH,MAAOY,EAAY,YACnB,OAAQA,EAAY,YAAA,CAE5B,MAAQ,CACJ,MAAO,CAAE,MAAO,EAAG,OAAQ,CAAA,CAC/B,CACJ,CAQA,OAAO,eAAeT,EAAkBU,EAAkB,IAAwB,CAC9E,OAAO,IAAI,QAAQ,CAACnB,EAASoB,IAAW,CACpC,MAAMd,EAAU,KAAK,cAAcG,CAAQ,EAC3C,GAAIH,EAAS,CACTN,EAAQM,CAAO,EACf,MACJ,CAEA,MAAMe,EAAW,IAAI,iBAAiB,IAAM,CACxC,MAAMf,EAAU,KAAK,cAAcG,CAAQ,EACvCH,IACAe,EAAS,WAAA,EACTrB,EAAQM,CAAO,EAEvB,CAAC,EAEDe,EAAS,QAAQ,SAAS,KAAM,CAC5B,UAAW,GACX,QAAS,EAAA,CACZ,EAED,WAAW,IAAM,CACbA,EAAS,WAAA,EACTD,EAAO,IAAI,MAAM,WAAWX,CAAQ,qBAAqBU,CAAO,IAAI,CAAC,CACzE,EAAGA,CAAO,CACd,CAAC,CACL,CACJ,CCtOO,MAAMG,EAAN,MAAMA,CAAgB,CAOzB,OAAO,gBAA0B,CAC7B,GAAI,KAAK,WAAa,KAClB,OAAO,KAAK,SAGhB,IAAIC,EAAQ,GACZ,MAAMC,EAAY,UAAU,WAAa,UAAU,QAAW,OAAe,MAGvEC,EAAc,2TAEdC,EAAe,4hDAErB,OAAID,EAAY,KAAKD,CAAS,GAAKE,EAAa,KAAKF,EAAU,OAAO,EAAG,CAAC,CAAC,KACvED,EAAQ,IAGZ,KAAK,SAAWA,EACTA,CACX,CAMA,OAAO,cAAuB,CAC1B,OAAO,KAAK,iBAAmB,WAAa,OAChD,CAMA,OAAO,iBAAkB,CACrB,MAAMI,EAAW,KAAK,eAAA,EACtB,MAAO,CACH,WAAYA,EAAW,QAAU,SACjC,QAASA,EAAW,EAAI,GACxB,SAAUA,EAAW,OAAS,OAC9B,QAASA,EAAW,WAAa,YACjC,iBAAkBA,EAAW,EAAI,CAAA,CAEzC,CAMA,OAAO,iBAAkB,CACrB,MAAMA,EAAW,KAAK,eAAA,EACtB,MAAO,CACH,eAAgBA,EAAW,OAAS,OACpC,iBAAkBA,EAAW,OAAS,OACtC,aAAcA,EAAW,MAAQ,MACjC,cAAeA,EAAW,SAAW,MACrC,WAAYA,EAAW,UAAY,QAAA,CAE3C,CAMA,OAAO,eAAyB,CAC5B,MAAO,iBAAkB,QAAU,UAAU,eAAiB,CAClE,CAMA,OAAO,eAAwB,CAC3B,MAAMC,EAAQ,OAAO,WAErB,OAAIA,EAAQ,IACD,KACAA,EAAQ,IACR,KACAA,EAAQ,IACR,KACAA,EAAQ,KACR,KAEA,IAEf,CAMA,OAAO,gBAAiB,CACpB,MAAO,CACH,GAAI,CAAE,IAAK,IAAK,cAAe,EAAG,SAAU,MAAA,EAC5C,GAAI,CAAE,IAAK,IAAK,IAAK,IAAK,cAAe,EAAG,SAAU,MAAA,EACtD,GAAI,CAAE,IAAK,IAAK,IAAK,IAAK,cAAe,EAAG,SAAU,MAAA,EACtD,GAAI,CAAE,IAAK,IAAK,IAAK,KAAM,cAAe,EAAG,SAAU,MAAA,EACvD,GAAI,CAAE,IAAK,KAAM,cAAe,EAAG,SAAU,MAAA,CAAO,CAE5D,CAMA,OAAO,sBAAuB,CAC1B,MAAMC,EAAa,KAAK,cAAA,EAExB,OADoB,KAAK,eAAA,EACNA,CAAsC,CAC7D,CACJ,EApHIP,EAAe,SAA2B,KADvC,IAAMQ,EAANR,ECCP,MAAAS,EAAe,CACb,IAAK,CACH,YAAa,2CACb,KAAM,gCACN,QAAS,OAAA,EAEX,IAAK,CACH,UAAW,CACT,YAAa,gCACb,UAAW,gCACX,aAAc,qCACd,aAAc,qCACd,WAAY,qCAAA,CACd,EAEF,KAAM,CACJ,aAAc,CACZ,yBAA0B,0BAAA,CAC5B,EAEF,GAAI,CACF,aAAc,CACZ,iBAAkB,+BAClB,eAAgB,+BAAA,EAElB,QAAS,CACP,UAAW,+BACX,QAAS,gCACT,OAAQ,+BACR,WAAY,qBACZ,WAAY,qBACZ,cAAe,wBACf,WAAY,oBAAA,EAEd,UAAW,CACT,SAAU,IACV,OAAQ,OACR,YAAa,GAAA,CACf,EAEF,OAAQ,CACN,WAAY,CACV,GAAI,CAAE,cAAe,EAAG,SAAU,OAAQ,QAAS,UAAA,EACnD,GAAI,CAAE,cAAe,EAAG,SAAU,OAAQ,QAAS,UAAA,EACnD,GAAI,CAAE,cAAe,EAAG,SAAU,OAAQ,QAAS,UAAA,EACnD,GAAI,CAAE,cAAe,EAAG,SAAU,OAAQ,QAAS,UAAA,EACnD,GAAI,CAAE,cAAe,EAAG,SAAU,OAAQ,QAAS,WAAA,CAAY,EAEjE,QAAS,CACP,iBAAkB,OAClB,aAAc,MACd,WAAY,KAAA,CACd,EAEF,SAAU,CACR,YAAa,GACb,gBAAiB,IACjB,YAAa,IACb,cAAe,EACf,WAAY,GAAA,EAEd,QAAS,CACP,cAAe,CACb,QAAS,UACT,UAAW,UACX,QAAS,UACT,OAAQ,UACR,QAAS,UACT,KAAM,UACN,MAAO,UACP,KAAM,SAAA,EAER,aAAc,CACZ,aAAc,MACd,WAAY,gBACZ,WAAY,MACZ,eAAgB,OAChB,QAAS,cACT,WAAY,SACZ,eAAgB,QAAA,CAClB,EAEF,cAAe,CACb,yBAA0B,GAC1B,mBAAoB,GACpB,kBAAmB,UACnB,iBAAkB,EAAA,EAEpB,YAAa,CACX,qBAAsB,GACtB,YAAa,GACb,cAAe,IACf,aAAc,GAAA,EAEhB,MAAO,CACL,cAAe,GACf,SAAU,OACV,4BAA6B,EAAA,CAEjC,ECjGO,MAAMC,CAAc,CAKf,aAAc,CAFtB,KAAQ,UAAiD,CAAA,EAGrD,KAAK,OAAS,CAAE,GAAGD,CAAA,CACvB,CAKA,OAAO,aAA6B,CAChC,OAAKC,EAAc,WACfA,EAAc,SAAW,IAAIA,GAE1BA,EAAc,QACzB,CAKA,WAAwC,CACpC,OAAO,KAAK,YAAY,KAAK,OAAQ,KAAK,SAAS,CACvD,CAKA,WAAuDC,EAA2C,CAE9F,OADe,KAAK,UAAA,EACNA,CAAO,CACzB,CAKA,aAAaC,EAAsD,CAC/D,KAAK,UAAY,CAAE,GAAG,KAAK,UAAW,GAAGA,CAAA,CAC7C,CAKA,gBAAyB,CACrB,OAAO,KAAK,WAAW,MAAM,EAAE,aAAa,wBAChD,CAKA,eAAeC,EAAwE,CACnF,OAAO,KAAK,WAAW,KAAK,EAAE,UAAUA,CAAQ,CACpD,CAKA,WAAWC,EAAsE,CAC7E,OAAO,KAAK,WAAW,IAAI,EAAE,QAAQA,CAAS,CAClD,CAKA,eAAeC,EAA6E,CACxF,OAAO,KAAK,WAAW,IAAI,EAAE,aAAaA,CAAW,CACzD,CAKA,qBAAoI,CAChI,MAAMR,EAAa,KAAK,qBAAA,EACxB,OAAO,KAAK,WAAW,QAAQ,EAAE,WAAWA,CAAU,CAC1D,CAKQ,sBAAiF,CACrF,MAAMD,EAAQ,OAAO,WAErB,OAAIA,EAAQ,IACD,KACAA,EAAQ,IACR,KACAA,EAAQ,IACR,KACAA,EAAQ,KACR,KAEA,IAEf,CAKA,mBACIU,EACyC,CACzC,OAAO,KAAK,WAAW,UAAU,EAAEA,CAAO,CAC9C,CAKA,gBACIA,EACwC,CACxC,OAAO,KAAK,WAAW,SAAS,EAAEA,CAAO,CAC7C,CAKA,gBAA0B,CACtB,OAAO,KAAK,WAAW,OAAO,EAAE,aACpC,CAKA,aAA+D,CAC3D,OAAO,KAAK,WAAW,OAAO,EAAE,QACpC,CAKA,gBAAyD,CACrD,MAAMC,EAAmB,CAAA,EACnBC,EAAS,KAAK,UAAA,EAGpB,OAAKA,EAAO,IAAI,aACZD,EAAO,KAAK,0BAA0B,EAGrCC,EAAO,KAAK,aAAa,0BAC1BD,EAAO,KAAK,+BAA+B,EAI3CC,EAAO,SAAS,gBAAkB,KAClCD,EAAO,KAAK,0CAA0C,EAGtDC,EAAO,SAAS,YAAc,KAC9BD,EAAO,KAAK,sCAAsC,EAGlDC,EAAO,SAAS,cAAgB,GAChCD,EAAO,KAAK,qCAAqC,EAG9C,CACH,QAASA,EAAO,SAAW,EAC3B,OAAAA,CAAA,CAER,CAKA,iBAAwB,CACpB,KAAK,OAAS,CAAE,GAAGR,CAAA,EACnB,KAAK,UAAY,CAAA,CACrB,CAKQ,YACJU,EACAP,EAC0B,CAC1B,MAAMQ,EAAS,CAAE,GAAGD,CAAA,EAEpB,UAAWlC,KAAO2B,EACd,GAAIA,EAAU,eAAe3B,CAAG,EAAG,CAC/B,MAAMC,EAAQ0B,EAAU3B,CAAuC,EAC3DC,GAAS,OAAOA,GAAU,UAAY,CAAC,MAAM,QAAQA,CAAK,EAC1DkC,EAAOnC,CAAuC,EAAI,CAC9C,GAAGmC,EAAOnC,CAAuC,EACjD,GAAGC,CAAA,EAGPkC,EAAOnC,CAAuC,EAAIC,CAE1D,CAGJ,OAAOkC,CACX,CAKA,cAAuB,CACnB,OAAO,KAAK,UAAU,KAAK,UAAA,EAAa,KAAM,CAAC,CACnD,CAKA,aAAaC,EAA0D,CACnE,GAAI,CACA,MAAMC,EAAiB,KAAK,MAAMD,CAAU,EAC5C,KAAK,aAAaC,CAAc,EAEhC,MAAMC,EAAa,KAAK,eAAA,EACxB,OAAKA,EAAW,QAOT,CAAE,QAAS,EAAA,EANP,CACH,QAAS,GACT,MAAO,oCAAoCA,EAAW,OAAO,KAAK,IAAI,CAAC,EAAA,CAKnF,OAASlC,EAAO,CACZ,MAAO,CACH,QAAS,GACT,MAAO,kCAAkCA,CAAK,EAAA,CAEtD,CACJ,CACJ,CCjOO,MAAMmC,CAA8B,CAKvC,aAAc,CACV,KAAK,WAAavD,EAA+B,YAAA,EACjD,KAAK,cAAgByC,EAAc,YAAA,EACnC,KAAK,YAAc,KAAK,cAAc,eAAe,kBAAkB,CAC3E,CAKA,MAAM,YAA4B,CAC9B,MAAM,KAAK,wBAAA,CACf,CAKA,MAAc,yBAAyC,CACnD,MAAMe,EAAc,MAAM,KAAK,WAAW,6BAAA,EAE1C,GAAI7C,EAAS,eAAe,KAAK,WAAW,GAAK6C,EAAY,SAAW,EACpE,OAGJ,MAAMC,EAAY,KAAK,uBAAA,EACjBC,EAAiB,KAAK,qBAAqBD,CAAS,EAE1D,KAAK,gBAAgBC,EAAgBF,CAAW,EAChD,KAAK,YAAYC,CAAS,CAC9B,CAKQ,wBAAsC,CAC1C,MAAMA,EAAY9C,EAAS,cAAc,MAAO,CAC5C,GAAI,KAAK,YACT,UAAW,8BAAA,CACd,EAGKgD,EAAShD,EAAS,cAAc,MAAO,CACzC,UAAW,8BAAA,EACZ,OAAO,EAEV,OAAAA,EAAS,YAAY8C,EAAWE,CAAM,EAC/BF,CACX,CAKQ,qBAAqBA,EAAqC,CAC9D,MAAMG,EAAUjD,EAAS,cAAc,MAAO,CAC1C,UAAW,+BAAA,CACd,EAED,OAAAA,EAAS,YAAY8C,EAAWG,CAAO,EAChCA,CACX,CAKQ,gBAAgBA,EAAsBC,EAA2B,CAE/C,KAAK,WAAW,iBAAA,EAExB,QAAQC,GAAc,CAChC,MAAM1D,EAAS,KAAK,aAAa0D,CAAU,EAC3CnD,EAAS,YAAYiD,EAASxD,CAAM,CACxC,CAAC,CACL,CAKQ,aAAa0D,EAA8B,CAC/C,MAAMC,EAAOD,EAAW,KAAOA,EAAW,OAAS,SAC7CE,EAAMF,EAAW,IAAMA,EAAW,MAAQ,IAC1CG,EAAOH,EAAW,KAAOA,EAAW,OAAS,GAC7CI,EAAQJ,EAAW,MAAQA,EAAW,QAAU,GAEhD1D,EAASO,EAAS,cAAc,MAAO,CACzC,UAAW,oBAAA,CACd,EAGKwD,EAAgB,KAAK,oBAAoBJ,EAAMC,EAAKC,EAAMC,CAAK,EACrE,OAAA9D,EAAO,UAAY+D,EAGnB,KAAK,oBAAoB/D,EAAQ4D,CAAG,EAE7B5D,CACX,CAKQ,oBAAoB2D,EAAcC,EAAaC,EAAcC,EAAuB,CACxF,MAAME,EAAWH,EAAO,aAAaA,CAAI,oCAAsC,GACzEI,EAAaH,EAAQ,UAAUA,CAAK,IAAM,GAEhD,MAAO;AAAA,uBACQF,CAAG,uDAAuDK,CAAU;AAAA;AAAA,sBAErED,CAAQ;AAAA,uDACyBL,CAAI;AAAA;AAAA;AAAA,SAIvD,CAKQ,oBAAoB3D,EAAqB4D,EAAmB,CAChErD,EAAS,iBAAiBP,EAAQmC,EAAgB,aAAA,EAAiBlB,GAAU,CACzEA,EAAM,eAAA,EACF2C,GAAOA,IAAQ,KACf,OAAO,KAAKA,EAAK,QAAQ,CAEjC,CAAC,CACL,CAKQ,YAAYP,EAA8B,CAE9C,MAAMa,EAAkB,KAAK,oBAAA,EAEzBA,IACA3D,EAAS,YAAY2D,EAAiBb,CAAS,EAC/C,KAAK,cAAcA,CAAS,EAEpC,CAKQ,qBAA0C,CAE9C,MAAMc,EAAqB,CACvB,sBACA,YACA,iBACA,eACA,MAAA,EAGJ,UAAWrD,KAAYqD,EAAoB,CACvC,MAAMd,EAAY9C,EAAS,cAAcO,CAAQ,EACjD,GAAIuC,EACA,OAAOA,CAEf,CAEA,OAAO,IACX,CAKQ,cAAcA,EAA8B,CAEhD9C,EAAS,UAAU8C,EAAW,CAC1B,QAAW,IACX,UAAa,oBACb,WAAc,eAAA,CACjB,EAGD,WAAW,IAAM,CACb9C,EAAS,UAAU8C,EAAW,CAC1B,QAAW,IACX,UAAa,gBACb,QAAW,OAAA,CACd,CACL,EAAG,GAAG,CACV,CAKA,MAAM,gBAAgC,CAClC,MAAMe,EAAoB7D,EAAS,eAAe,KAAK,WAAW,EAC9D6D,GACA7D,EAAS,cAAc6D,CAAiB,EAG5C,MAAM,KAAK,WAAW,gCAAA,EACtB,MAAM,KAAK,wBAAA,CACf,CAKA,aAAoB,CAChB,MAAMf,EAAY9C,EAAS,eAAe,KAAK,WAAW,EACtD8C,GACA9C,EAAS,UAAU8C,EAAW,CAAE,QAAW,OAAQ,CAE3D,CAKA,aAAoB,CAChB,MAAMA,EAAY9C,EAAS,eAAe,KAAK,WAAW,EACtD8C,GACA9C,EAAS,UAAU8C,EAAW,CAAE,QAAW,QAAS,CAE5D,CAKA,mBAA6B,CACzB,MAAMA,EAAY9C,EAAS,eAAe,KAAK,WAAW,EAC1D,OAAO8C,IAAc,MAAQA,EAAU,MAAM,UAAY,MAC7D,CACJ,CClOAvD,EAAI,aAAa,IAAI,2CAA4C,IAAM,CAErEA,EAAI,MAAM,OAAO,yBAA2BJ,EAG5C,MAAM2E,EAAgBhC,EAAc,YAAA,EAG9BiC,EAAY,IAAInB,EAChBoB,EAAa3E,EAA+B,YAAA,EA4BlD,GAzBA4E,EAAAA,OAAOC,EAAc,UAAW,OAAQ,SAAUC,EAAQ,CAEpD,KAAK,qBAEP,WAAW,IAAM,CACfJ,EAAU,aAAa,MAAM,IAAM,CAEnC,CAAC,CACH,EAAG,GAAG,CAEV,CAAC,EAGDG,EAAc,UAAU,kBAAoB,UAAY,CACtD,MAAME,EAAc,OAAO,SAAS,SAKpC,MAFqB,CAAC,IAAK,QAAS,MAAO,KAAK,EAE5B,KAAKC,GACvBD,IAAgBC,GAAQD,EAAY,WAAWC,CAAI,CAAA,CAEvD,EAGIP,EAAc,mBAAmB,aAAa,EAAG,CACnD,MAAMQ,EAAkBR,EAAc,mBAAmB,iBAAiB,EAE1E,YAAY,IAAM,CACZC,EAAU,qBACZC,EAAW,kCAAkC,MAAM,IAAM,CAEzD,CAAC,CAEL,EAAGM,CAAe,CACpB,CAGC,OAAe,qBAAuB,CACrC,UAAAP,EACA,WAAAC,EACA,cAAAF,EAGA,eAAgB,IAAMC,EAAU,eAAA,EAChC,YAAa,IAAMA,EAAU,YAAA,EAC7B,YAAa,IAAMA,EAAU,YAAA,EAC7B,eAAgB,IAAMC,EAAW,4BAAA,EAGjC,GAAIF,EAAc,kBAAoB,CACpC,MAAO,CACL,UAAW,IAAMA,EAAc,UAAA,EAC/B,SAAU,KAAO,CACf,UAAWE,EAAW,UAAA,EACtB,QAASA,EAAW,QAAA,EACpB,UAAWA,EAAW,aAAA,EACtB,kBAAmBD,EAAU,kBAAA,CAAkB,GAEjD,YAAa,IAAMC,EAAW,YAAA,EAC9B,aAAc,IAAMF,EAAc,aAAA,CAAa,CACjD,CACF,CAIJ,CAAC"}